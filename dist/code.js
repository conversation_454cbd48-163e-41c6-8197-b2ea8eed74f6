"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
figma.showUI(__uiFiles__.ui, { width: 400, height: 600 });
// --- Load KPIs Dynamically ---
function loadKPIs() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const response = yield fetch('public/data/master_kpis.json');
            const data = yield response.json();
            return data;
        }
        catch (error) {
            console.error('Error loading KPIs:', error);
            return [];
        }
    });
}
// --- Handle Messages from the UI ---
figma.ui.onmessage = (msg) => __awaiter(void 0, void 0, void 0, function* () {
    switch (msg.type) {
        case 'show-browse':
            figma.showUI(__uiFiles__.browse, { width: 400, height: 600 });
            break;
        case 'show-wizard':
            figma.showUI(__uiFiles__.wizard, { width: 400, height: 600 });
            break;
        case 'show-start':
            figma.showUI(__uiFiles__.ui, { width: 400, height: 600 });
            break;
        case 'show-loading':
            figma.showUI(__uiFiles__.loading, { width: 400, height: 600 });
            break;
        case 'loading-complete':
            // Simulate KPI recommendation process
            setTimeout(() => {
                figma.showUI(__uiFiles__.browse, { width: 400, height: 600 });
            }, 1000);
            break;
        default:
            console.log('Unknown message type:', msg.type);
    }
});
