<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Load fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom Styles -->

    <style>
      /* Base font styles */
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        margin: 0;
        padding: 20px;
        background: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        box-sizing: border-box;
      }

      .button-container {
        display: flex;
        gap: 16px;
        margin-top: 24px;
      }

      button {
        padding: 12px 24px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        background: #f5f5f5;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
      }

      button:hover {
        background: #e0e0e0;
      }

      button.primary {
        background: #0C8CE9;
        color: white;
        border: none;
      }

      button.primary:hover {
        background: #0a7acc;
      }

      .title {
        font-size: 24px;
        margin: 24px 0 16px;
        color: #333;
      }

      .description {
        text-align: center;
        color: #666;
        max-width: 400px;
        line-height: 1.5;
        margin-bottom: 24px;
      }
    </style>
    <link rel="stylesheet" crossorigin href="/assets/ui-DVT-lq3k.css">
    <link rel="stylesheet" crossorigin href="/assets/global-SbDuovmt.css">
  </head>

  <body>
    <!-- Logo SVG -->
    <img class="bullseye-graphic-icon" alt="Product KPIs logo" src="../logo/Logo4.svg" width="170" height="170">

    <!-- Main Title -->
    <h1 class="title">Let's get started!</h1>

    <!-- Description Text -->
    <div class="description">
      <p>We'll ask <strong>6 questions</strong> about your product.</p>
      <p>Answer if you can, or just leave them blank.</p>
      <p>(Don't worry, this won't take long.)</p>
    </div>

    <!-- Action Buttons -->
    <div class="button-container">
      <button id="browseBtn">
        Browse KPIs
      </button>
      <button id="wizardBtn" class="primary">
        Recommend KPIs
      </button>
    </div>

    <!-- Footer -->
    <div class="footer" style="margin-top: 40px;">
      <a href="https://buymeacoffee.com/arsonistai" target="_blank" style="display: flex; align-items: center; gap: 8px; text-decoration: none; color: #0C8CE9;">
        <span>Buy us a coffee</span>
        <svg width="16" height="16" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7.5 1.5V3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M10.5 1.5V3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 6C12.1989 6 12.3897 6.07902 12.5303 6.21967C12.671 6.36032 12.75 6.55109 12.75 6.75V12.75C12.75 13.5456 12.4339 14.3087 11.8713 14.8713C11.3087 15.4339 10.5456 15.75 9.75 15.75H5.25C4.45435 15.75 3.69129 15.4339 3.12868 14.8713C2.56607 14.3087 2.25 13.5456 2.25 12.75V6.75C2.25 6.55109 2.32902 6.36032 2.46967 6.21967C2.61032 6.07902 2.80109 6 3 6H13.5C14.2956 6 15.0587 6.31607 15.6213 6.87868C16.1839 7.44129 16.5 8.20435 16.5 9C16.5 9.79565 16.1839 10.5587 15.6213 11.1213C15.0587 11.6839 14.2956 12 13.5 12H12.75" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M4.5 1.5V3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </a>
    </div>

    <script>
      // Wait for the DOM to be fully loaded
      document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM fully loaded');

        // Initialize buttons
        const browseBtn = document.getElementById('browseBtn');
        const wizardBtn = document.getElementById('wizardBtn');

        if (browseBtn) {
          browseBtn.addEventListener('click', (e) => {
            console.log('Browse button clicked');
            parent.postMessage({ pluginMessage: { type: 'show-browse' } }, '*');
          });
          console.log('Browse button initialized');
        }

        if (wizardBtn) {
          wizardBtn.addEventListener('click', (e) => {
            console.log('Wizard button clicked');
            parent.postMessage({ pluginMessage: { type: 'show-wizard' } }, '*');
          });
          console.log('Wizard button initialized');
        }
      });

      // Log any errors
      window.addEventListener('error', (event) => {
        console.error('Script error:', event);
      });
    </script>
  </body>
</html>