<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Load fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shoelace CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/themes/light.css">

    <!-- Custom Styles -->

    <style>
      /* Base font styles */
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        margin: 0;
        padding: 20px;
        background: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        box-sizing: border-box;
      }

      .button-container {
        display: flex;
        gap: 16px;
        margin-top: 24px;
      }

      .title {
        font-size: 24px;
        margin: 24px 0 16px;
        color: #333;
      }

      .description {
        text-align: center;
        color: #666;
        max-width: 400px;
        line-height: 1.5;
        margin-bottom: 24px;
      }
    </style>
    <script type="module" crossorigin src="/assets/global-C98O8uut.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/global-SbDuovmt.css">
    <link rel="stylesheet" crossorigin href="/assets/ui-DVT-lq3k.css">
  </head>

  <body>
    <!-- Logo SVG - Embedded inline to avoid loading issues -->
    <svg class="bullseye-graphic-icon" xmlns="http://www.w3.org/2000/svg" width="170" height="169" fill="currentColor" viewBox="0 0 170 169">
      <path d="M170.002 22.829c-.099-1.619-1.538-1.124-2.693-1.521l-19.199 3.547c.634-4.55.703-9.153 1.136-13.73.165-1.742.6-3.483.735-5.237.061-.793.178-2.682.081-3.385-.15-1.078-2.333-2.926-3.407-2.43-.167.077-1.02.994-1.222 1.217-2.404 2.663-4.48 5.134-7.045 7.723-2.947 2.977-5.906 5.926-8.697 9.074-.298.337-1.395 1.91-1.558 2.012-.198.124-.567.049-.818.197-.065.039-.993 1.023-1.048 1.103-1.093 1.571-.612 2.438-.582 4.055.017 1.058-.127 2.12-.208 3.09-.107 1.283.075 2.584-.065 3.867l.495.41-.646.085c-.11.309.294.203.326.345.162.704-.593 1.25-.652 1.78-.117 1.034-.292 4.248-.131 5.188.057.334.431.724.4.977-.006.056-.527.896-.59.976-1.801 2.27-4.799 4.455-6.916 6.47-2.843 2.707-7.131 6.331-9.541 9.202-.682.812-1.271 1.839-2.023 2.684-.528.593-1.258.952-1.673 1.709-.568 1.032-.579.762-1.408 1.542-4.93 4.626-9.69 9.504-14.66 14.064-.386.355-2.156 1.965-2.46 2.084-.108.04-.382.09-.384-.059.583-.872.343-1.129.325-1.949-.005-.253.165-.49.131-.776a15.659 15.659 0 0 0-.38-1.086c-.171-.262-.668-.32-.927-.498-3.001-2.063-6.781-2.004-10.26-1.47-.58.089-1.247-.035-1.852.175l.647.326c-.144.395-.824.58-1.19.49-.124-.032-.1-.315-.175-.312-3.321.126-6.759 5.419-8.66 7.719-3.246 7.317-2.388 17.217 4.597 22.006 4.8 3.289 8.996 4.658 14.892 3.232 7.083-1.715 12.889-9.902 12.594-17.124-.093-2.243-.751-3.314-2.012-5.067-.224-.31-.937-.405-.692-1.196.042-.135 1.555-1.986 1.774-2.214 1.944-2.027 4.422-3.674 6.539-5.47.263-.223.878-.697 1.03-.846.155-.151-.079-.971.638-.892-.149 1.756.892 2.928 1.58 4.347.415.857.581 1.698.896 2.516.384 1.002.974 1.707 1.21 3.011 2.512 13.928-7.162 31.085-21.642 33.469-7.828 1.29-14.76.797-21.631-3.369-7.88-4.777-12.454-12.38-12.929-21.685-.428-8.356 1.93-16.125 7.459-22.272 2.623-2.917 10.838-7.582 14.673-8.04 6.296-.752 11.075-.673 17.093 1.379.182.061 1.877.437 1.62-.162-.153-.36-.846-.294-.645-.894.184 0 .375-.028.558.006.167.033 2.414 1.191 2.678 1.378.27.19.333.399.335.724.282.259 1.669-.85 1.864-1.127.313-.45-.636-.254-.081-.734.584-.504 3.014-.498 2.576-1.773-.073-.212-.39-.212-.448-.298-.06-.088.109-.31.04-.452-.577-1.209-5.819-4.268-7.25-4.904-10.931-4.856-25.437-1.492-34.616 5.65-6.962 5.416-13.544 15.993-14.111 24.851-.576 8.973 1.05 19.15 6.805 26.327 3.765 4.694 8.636 7.918 13.62 11.045.323.203 1.054.102 1.45.336.248.147.242.469.453.558.553.236 1.708.363 2.306.573 8.92 3.109 17.078 2.37 25.88-.861 4.795-1.76 13.759-9.153 16.227-13.629 2.994-5.435 5.415-10.562 5.733-16.872.287-5.69-.224-11.163-2.544-16.334-1.087-2.422-1.984-3.434-3.486-5.441-.201-.268-.193-.631-.31-.79-.183-.254-.866-.06-.798-.804.008-.081.827-1.202.942-1.339 2.069-2.447 4.76-4.37 6.978-6.98.832-.98 1.974-1.654 2.91-2.523.102 1.458 1.176 2.653 1.843 3.922 4.325 8.216 5.449 12.154 6.247 21.511 1.554 18.256-5.19 33.215-18.865 44.935-8.638 7.403-22.312 10.414-33.556 9.92-7.407-.325-17.44-3.815-23.402-8.225-10.327-7.637-18.494-18.371-20.282-31.341-.913-6.622-1.157-12.016.227-18.65 1.635-7.84 4.404-13.92 8.89-20.5 3.192-4.687 6.398-8.147 11.264-11.132 5.612-3.444 11.57-6.203 18.002-7.626 7.18-1.59 12.45-1.225 19.608-.078 5.598.898 10.29 2.663 15.096 5.502 1.632.963 3.264 2.052 4.804 3.147.337.057.189-.05.258-.223.256-.65-.052-1.594-.829-1.565l.118-.325c.972-.047.083-.504-.063-.701-.098-.132-.075-.37-.216-.51-.389-.383-1.095-.346-.972-1.387.367.266.858.397 1.222.65.895.62 1.985 2.125 3.003 2.669.79.421 1.902.376 2.66 1.072l.242-.004c-.12-.871.816-.504 1.007-1.044.114-.322-.036-2.92-.211-3.23-.19-.336-1.71-1.676-2.097-1.975-.351-.27-1.001-.638-1.465-.968-8.394-5.985-17.282-9.301-27.622-10.167-12.032-1.007-23.854 1.781-34.1 8.054-11.177 6.843-19.212 15.997-23.96 28.302-4.462 11.564-5.063 22.623-2.06 34.653 1.873 7.508 4.712 14.569 9.153 20.885 8.525 10.969 20.187 19.298 33.97 21.681 9.864 1.704 19.416 1.587 28.94-1.66.558-.19 1.948-1.158 2.065-1.184.487-.114.715.117 1.06.095 1.161-.074 3.833-1.764 4.998-2.305 1.072-.5 1.915-.56 3.083-1.295 12.746-8.034 21.495-18.665 25.805-33.279 4.184-14.184 3.012-25.329-1.66-39.14-1.036-3.062-3.311-7.16-5.243-9.579-.092-.117-.073-.369-.193-.488a23.561 23.561 0 0 0-1.327-.865c-.546-.218-1.497-.278-1.93.167-.154.158-.063.397-.209.511-.236.18-.654-.037-.56.614l-1.445-.06 4.122-4.152c3.192-2.477 5.528-5.872 8.521-8.52.654-.204 1.345-.017 2.008-.098 3.459-.428 7.062-.966 10.55-1.285 1.838-.168 4.751.283 6.227-.906.641-.516 1.535-1.59 2.189-2.193 4.652-4.294 9.183-8.831 13.943-12.997.858-.752 2.938-2.476 3.132-3.528.091-.492-.104-.74-.099-1.098.004-.308.166-.604.143-.973l.005-.008Z"/>
      <path d="M155.199 91.2c-.261-13.15-1.77-24.404-7.715-36.305-.363-.727-2.607-4.751-2.978-5.093-.667-.615-1.751-.62-2.565-.333-.404.141-.683.46-1.094.527-.556.09-2.679-.27-2.671.572 0 .098.689 1.473.819 1.759 1.715 3.774 3.781 7.772 5.262 11.82 4.111 11.243 4.762 22.644 3.323 34.529-2.135 17.636-10.804 34.402-24.716 45.429-6.168 4.889-14.305 10.384-21.923 12.625-9.234 2.718-19.147 4.548-28.8 3.751-12.821-1.059-21.583-4.227-32.225-11.208-1.353-.886-2.165-1.457-3.348-2.492-3.55-3.11-7.298-5.875-10.642-9.236-5.178-5.205-8.365-11.198-11.602-17.7-.258-.517-.965-1.423-1.171-1.913-.216-.514-.123-1.371-.362-1.909-.576-1.303-1.134-3.016-1.622-4.552-1.569-4.927-2.193-10.409-2.792-15.728-1.072-9.54.513-23.86 4.572-32.605 6.083-13.11 16.759-25.575 29.234-32.927 11.474-6.762 22.026-10.486 35.487-11.023.487-.02 1.132.228 1.678.106.076-.462-.096-.272-.373-.346-.533-.145-1.65-.381-2.18-.424-.406-.032-.815.225-1.227.122-.123-.03-.102-.318-.211-.333-.311-.04-.934.322-1.356.17-.127-.434.444-.464.727-.489 5.444-.462 9.983-.122 15.323.888.446.085.837.339 1.218.404.701.117 1.587-.062 2.333.18l.007.644c.858.044 1.967-.362 2.806-.16.123.029.099.29.222.318.698.16 3.767.5 4.233.848.129.097.087.459.232.58.147.124 2.381.791 2.83.981a85.43 85.43 0 0 1 9.831 4.93c.256.15.501.562.704.613.269.066.938-.536 1.507-.147l-.324.404.397.245c.498-.395 1.363-.43 1.819-.706.562-.338.953-1.215.793-1.895-.255-1.078-1.157-.898-1.317-1.129-.116-.164-.043-.555-.159-.816-1.113-2.505-10.53-7.405-13.283-8.457-5.34-2.042-10.728-3.477-16.37-4.225-4.179-.555-8.098-.512-12.31-.495-7.564.035-16.077 2.02-23.103 4.64-11.415 4.255-20.193 10.64-28.786 19.076-7.427 7.29-13.91 16.714-17.885 26.27C.685 72.436-.342 83.887.093 96.248c.223 6.38 1.453 10.621 3.012 16.643 2.542 9.812 6.909 18.218 13.138 26.153 4.766 6.071 9.455 10.619 15.862 14.972 2.39 1.622 5.183 3.8 7.748 5.069 2.038 1.008 4.069 2.243 6.163 3.245 6.129 2.935 13.946 4.973 20.61 5.824 3.949.504 8.174.935 12.163.818 4.183-.124 8.459-.687 12.59-1.515 8.473-1.695 15.759-3.961 23.284-8.18 2.507-1.406 4.752-3.019 7.164-4.517 1.561-.972 2.787-2.161 4.281-3.345 7.005-5.55 12.332-10.585 17.21-18.088 7.672-11.8 12.16-28.064 11.881-42.126Z"/>
    </svg>

    <!-- Main Title -->
    <h1 class="title">Let's get started!</h1>

    <!-- Description Text -->
    <div class="description">
      <p>We'll ask <strong>6 questions</strong> about your product.</p>
      <p>Answer if you can, or just leave them blank.</p>
      <p>(Don't worry, this won't take long.)</p>
    </div>

    <!-- Action Buttons -->
    <div class="button-container">
      <sl-button id="browseBtn" variant="default" size="large">
        Browse KPIs
      </sl-button>
      <sl-button id="wizardBtn" variant="primary" size="large">
        Recommend KPIs
      </sl-button>
    </div>

    <!-- Footer -->
    <div class="footer" style="margin-top: 40px;">
      <a href="https://buymeacoffee.com/arsonistai" target="_blank" style="display: flex; align-items: center; gap: 8px; text-decoration: none; color: #0C8CE9;">
        <span>Buy us a coffee</span>
        <svg width="16" height="16" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7.5 1.5V3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M10.5 1.5V3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 6C12.1989 6 12.3897 6.07902 12.5303 6.21967C12.671 6.36032 12.75 6.55109 12.75 6.75V12.75C12.75 13.5456 12.4339 14.3087 11.8713 14.8713C11.3087 15.4339 10.5456 15.75 9.75 15.75H5.25C4.45435 15.75 3.69129 15.4339 3.12868 14.8713C2.56607 14.3087 2.25 13.5456 2.25 12.75V6.75C2.25 6.55109 2.32902 6.36032 2.46967 6.21967C2.61032 6.07902 2.80109 6 3 6H13.5C14.2956 6 15.0587 6.31607 15.6213 6.87868C16.1839 7.44129 16.5 8.20435 16.5 9C16.5 9.79565 16.1839 10.5587 15.6213 11.1213C15.0587 11.6839 14.2956 12 13.5 12H12.75" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M4.5 1.5V3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </a>
    </div>

    <script>
      // Wait for the DOM to be fully loaded
      document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM fully loaded');

        // Initialize buttons
        const browseBtn = document.getElementById('browseBtn');
        const wizardBtn = document.getElementById('wizardBtn');

        if (browseBtn) {
          browseBtn.addEventListener('click', (e) => {
            console.log('Browse button clicked');
            parent.postMessage({ pluginMessage: { type: 'show-browse' } }, '*');
          });
          console.log('Browse button initialized');
        }

        if (wizardBtn) {
          wizardBtn.addEventListener('click', (e) => {
            console.log('Wizard button clicked');
            parent.postMessage({ pluginMessage: { type: 'show-wizard' } }, '*');
          });
          console.log('Wizard button initialized');
        }
      });

      // Log any errors
      window.addEventListener('error', (event) => {
        console.error('Script error:', event);
      });
    </script>

    <script type="module" src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/shoelace-autoloader.js"></script>
  </body>
</html>