<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <!-- Load fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Import CSS via JavaScript -->
  <script type="module" crossorigin src="/assets/global-CQQUB0GY.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/global-CJ-aCwk9.css">
  <link rel="stylesheet" crossorigin href="/assets/browse-BqP8r6XT.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <button id="backBtn" class="back-button">← Back</button>
      <h1 class="title">Browse KPIs</h1>
    </div>
    
    <div class="content">
      <div id="kpiList" class="kpi-list">
        <!-- KPIs will be loaded here -->
        <div class="kpi-card">
          <h3>Monthly Active Users (MAU)</h3>
          <p>Number of unique users who engage with your product in a month.</p>
        </div>
        <div class="kpi-card">
          <h3>Conversion Rate</h3>
          <p>Percentage of users who take a desired action.</p>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    document.getElementById('backBtn').addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-start' } }, '*');
    });
  </script>
</body>
</html> 
