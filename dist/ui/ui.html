<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <!-- Load fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Import CSS via JavaScript -->
  <script type="module" crossorigin src="/assets/global-CQQUB0GY.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/global-CJ-aCwk9.css">
  <link rel="stylesheet" crossorigin href="/assets/ui-P7u0WpuM.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 class="title">Product KPIs</h1>
    </div>
    
    <div class="content">
      <p class="description">
        Find the perfect KPIs for your product with our AI-powered recommendation engine.
      </p>
      
      <div class="button-container">
        <button id="wizardBtn" class="primary-button">
          <span>KPI Wizard</span>
        </button>
        
        <button id="browseBtn" class="secondary-button">
          <span>Browse All KPIs</span>
        </button>
      </div>
    </div>
    
    <div class="footer">
      <p class="footer-text">Powered by ArsonistAI</p>
    </div>
  </div>
  
  <script>
    document.getElementById('wizardBtn').addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-wizard' } }, '*');
    });
    
    document.getElementById('browseBtn').addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-browse' } }, '*');
    });
  </script>
</body>
</html>
