import"./global-C98O8uut.js";document.querySelectorAll("sl-button[data-filter]").forEach(e=>{e.addEventListener("click",()=>{document.querySelectorAll("sl-button[data-filter]").forEach(s=>s.variant="default"),e.variant="primary";const t=e.dataset.filter;r(t)})});function r(e){document.querySelectorAll(".card-list").forEach(s=>{e==="all"||s.dataset.category===e?s.style.display="block":s.style.display="none"})}document.getElementById("searchInput").addEventListener("sl-input",e=>{const t=e.target.value.toLowerCase();document.querySelectorAll(".card-list").forEach(l=>{const o=l.querySelector('[slot="header"] strong').textContent.toLowerCase(),n=l.querySelector("p").textContent.toLowerCase();o.includes(t)||n.includes(t)?l.style.display="block":l.style.display="none"})});document.querySelectorAll(".card-list").forEach(e=>{e.addEventListener("click",()=>{const t=e.querySelector('[slot="header"] strong').textContent;parent.postMessage({pluginMessage:{type:"select-kpi",kpi:t}},"*")})});document.getElementById("backBtn").addEventListener("click",()=>{console.log("Back button clicked in browse"),parent.postMessage({pluginMessage:{type:"show-start"}},"*")});document.addEventListener("focusin",e=>{e.preventDefault(),e.stopPropagation()});
