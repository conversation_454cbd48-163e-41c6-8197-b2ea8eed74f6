import"./modulepreload-polyfill-B5Qt9EMX.js";/* empty css               */let t=null,a=1;document.querySelectorAll(".card-pill").forEach(e=>{e.addEventListener("click",()=>{document.querySelectorAll(".card-pill").forEach(s=>{s.classList.remove("selected")}),e.classList.add("selected"),t=e.dataset.value,document.getElementById("nextBtn").disabled=!1})});document.getElementById("backBtn").addEventListener("click",()=>{parent.postMessage({pluginMessage:{type:"show-start"}},"*")});document.getElementById("nextBtn").addEventListener("click",()=>{t&&a===1&&(parent.postMessage({pluginMessage:{type:"wizard-step",step:2,data:{category:t}}},"*"),parent.postMessage({pluginMessage:{type:"show-loading"}},"*"))});document.addEventListener("focusin",e=>{e.preventDefault(),e.stopPropagation()});
