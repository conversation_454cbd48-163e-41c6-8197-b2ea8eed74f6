/**
 * @license
 * Copyright 2023 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
type ARIAAttributeMap = {
    [K in keyof ARIAMixin]: string;
};
/**
 * Map of ARIAMixin properties to attributes
 */
export declare const ariaMixinAttributes: ARIAAttributeMap;
export declare const ElementInternalsShim: {
    new (_host: HTMLElement): {
        ariaAtomic: string;
        ariaAutoComplete: string;
        ariaBrailleLabel: string;
        ariaBrailleRoleDescription: string;
        ariaBusy: string;
        ariaChecked: string;
        ariaColCount: string;
        ariaColIndex: string;
        ariaColSpan: string;
        ariaCurrent: string;
        ariaDescription: string;
        ariaDisabled: string;
        ariaExpanded: string;
        ariaHasPopup: string;
        ariaHidden: string;
        ariaInvalid: string;
        ariaKeyShortcuts: string;
        ariaLabel: string;
        ariaLevel: string;
        ariaLive: string;
        ariaModal: string;
        ariaMultiLine: string;
        ariaMultiSelectable: string;
        ariaOrientation: string;
        ariaPlaceholder: string;
        ariaPosInSet: string;
        ariaPressed: string;
        ariaReadOnly: string;
        ariaRequired: string;
        ariaRoleDescription: string;
        ariaRowCount: string;
        ariaRowIndex: string;
        ariaRowSpan: string;
        ariaSelected: string;
        ariaSetSize: string;
        ariaSort: string;
        ariaValueMax: string;
        ariaValueMin: string;
        ariaValueNow: string;
        ariaValueText: string;
        role: string;
        __host: HTMLElement;
        readonly shadowRoot: ShadowRoot;
        checkValidity(): boolean;
        form: null;
        labels: NodeListOf<HTMLLabelElement>;
        reportValidity(): boolean;
        setFormValue(): void;
        setValidity(): void;
        states: Set<string>;
        validationMessage: string;
        validity: ValidityState;
        willValidate: boolean;
    };
};
declare const ElementInternalsShimWithRealType: typeof ElementInternals;
export { ElementInternalsShimWithRealType as ElementInternals };
export declare const HYDRATE_INTERNALS_ATTR_PREFIX = "hydrate-internals-";
//# sourceMappingURL=element-internals.d.ts.map