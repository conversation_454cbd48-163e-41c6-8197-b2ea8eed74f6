{"version": 3, "file": "tinycolor.umd.min.js", "sources": ["../module/css-color-names.js", "../module/util.js", "../module/conversion.js", "../module/format-input.js", "../module/index.js", "../module/random.js", "../module/readability.js", "../module/umd_api.js", "../module/from-ratio.js", "../module/to-ms-filter.js"], "sourcesContent": ["// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nexport const names = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    black: '#000000',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkgrey: '#a9a9a9',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkslategrey: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dimgrey: '#696969',\n    dodgerblue: '#1e90ff',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    goldenrod: '#daa520',\n    gold: '#ffd700',\n    gray: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    grey: '#808080',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavenderblush: '#fff0f5',\n    lavender: '#e6e6fa',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgray: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightgrey: '#d3d3d3',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslategray: '#778899',\n    lightslategrey: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370db',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#db7093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    rebeccapurple: '#663399',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    slategrey: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    wheat: '#f5deb3',\n    white: '#ffffff',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32',\n};\n", "/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nexport function bound01(n, max) {\n    if (isOnePointZero(n)) {\n        n = '100%';\n    }\n    const isPercent = isPercentage(n);\n    n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n    // Automatically convert percentage into number\n    if (isPercent) {\n        n = parseInt(String(n * max), 10) / 100;\n    }\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n        return 1;\n    }\n    // Convert into [0, 1] range if it isn't already\n    if (max === 360) {\n        // If n is a hue given in degrees,\n        // wrap around out-of-range values into [0, 360] range\n        // then convert into [0, 1].\n        n = (n < 0 ? (n % max) + max : n % max) / parseFloat(String(max));\n    }\n    else {\n        // If n not a hue given in degrees\n        // Convert into [0, 1] range if it isn't already.\n        n = (n % max) / parseFloat(String(max));\n    }\n    return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nexport function clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nexport function isOnePointZero(n) {\n    return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nexport function isPercentage(n) {\n    return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nexport function boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n        a = 1;\n    }\n    return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nexport function convertToPercentage(n) {\n    if (Number(n) <= 1) {\n        return `${Number(n) * 100}%`;\n    }\n    return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nexport function pad2(c) {\n    return c.length === 1 ? '0' + c : String(c);\n}\n", "import { bound01, pad2 } from './util.js';\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */\nexport function rgbToRgb(r, g, b) {\n    return {\n        r: bound01(r, 255) * 255,\n        g: bound01(g, 255) * 255,\n        b: bound01(b, 255) * 255,\n    };\n}\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */\nexport function rgbToHsl(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    const max = Math.max(r, g, b);\n    const min = Math.min(r, g, b);\n    let h = 0;\n    let s = 0;\n    const l = (max + min) / 2;\n    if (max === min) {\n        s = 0;\n        h = 0; // achromatic\n    }\n    else {\n        const d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h, s, l };\n}\nfunction hue2rgb(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    if (t > 1) {\n        t -= 1;\n    }\n    if (t < 1 / 6) {\n        return p + (q - p) * (6 * t);\n    }\n    if (t < 1 / 2) {\n        return q;\n    }\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hslToRgb(h, s, l) {\n    let r;\n    let g;\n    let b;\n    h = bound01(h, 360);\n    s = bound01(s, 100);\n    l = bound01(l, 100);\n    if (s === 0) {\n        // achromatic\n        g = l;\n        b = l;\n        r = l;\n    }\n    else {\n        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n        const p = 2 * l - q;\n        r = hue2rgb(p, q, h + 1 / 3);\n        g = hue2rgb(p, q, h);\n        b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */\nexport function rgbToHsv(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    const max = Math.max(r, g, b);\n    const min = Math.min(r, g, b);\n    let h = 0;\n    const v = max;\n    const d = max - min;\n    const s = max === 0 ? 0 : d / max;\n    if (max === min) {\n        h = 0; // achromatic\n    }\n    else {\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h, s, v };\n}\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hsvToRgb(h, s, v) {\n    h = bound01(h, 360) * 6;\n    s = bound01(s, 100);\n    v = bound01(v, 100);\n    const i = Math.floor(h);\n    const f = h - i;\n    const p = v * (1 - s);\n    const q = v * (1 - f * s);\n    const t = v * (1 - (1 - f) * s);\n    const mod = i % 6;\n    const r = [v, q, p, p, t, v][mod];\n    const g = [t, v, v, q, p, p][mod];\n    const b = [p, p, t, v, v, q][mod];\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color to hex\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255]\n * *Returns:* a 3 or 6 character hex\n */\nexport function rgbToHex(r, g, b, allow3Char) {\n    const hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    // Return a 3 character hex if possible\n    if (allow3Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * *Assumes:* r, g, b are contained in the set [0, 255] and a in [0, 1]\n * *Returns:* a 4 or 8 character rgba hex\n */\n// eslint-disable-next-line max-params\nexport function rgbaToHex(r, g, b, a, allow4Char) {\n    const hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n        pad2(convertDecimalToHex(a)),\n    ];\n    // Return a 4 character hex if possible\n    if (allow4Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1)) &&\n        hex[3].startsWith(hex[3].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n *\n * *Assumes:* r, g, b are contained in the set [0, 255] and a in [0, 1]\n * *Returns:* a 8 character argb hex\n */\nexport function rgbaToArgbHex(r, g, b, a) {\n    const hex = [\n        pad2(convertDecimalToHex(a)),\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    return hex.join('');\n}\n/**\n * Converts CMYK to RBG\n * Assumes c, m, y, k are in the set [0, 100]\n */\nexport function cmykToRgb(c, m, y, k) {\n    const cConv = c / 100;\n    const mConv = m / 100;\n    const yConv = y / 100;\n    const kConv = k / 100;\n    const r = 255 * (1 - cConv) * (1 - kConv);\n    const g = 255 * (1 - mConv) * (1 - kConv);\n    const b = 255 * (1 - yConv) * (1 - kConv);\n    return { r, g, b };\n}\nexport function rgbToCmyk(r, g, b) {\n    let c = 1 - r / 255;\n    let m = 1 - g / 255;\n    let y = 1 - b / 255;\n    let k = Math.min(c, m, y);\n    if (k === 1) {\n        c = 0;\n        m = 0;\n        y = 0;\n    }\n    else {\n        c = ((c - k) / (1 - k)) * 100;\n        m = ((m - k) / (1 - k)) * 100;\n        y = ((y - k) / (1 - k)) * 100;\n    }\n    k *= 100;\n    return {\n        c: Math.round(c),\n        m: Math.round(m),\n        y: Math.round(y),\n        k: Math.round(k),\n    };\n}\n/** Converts a decimal to a hex value */\nexport function convertDecimalToHex(d) {\n    return Math.round(parseFloat(d) * 255).toString(16);\n}\n/** Converts a hex value to a decimal */\nexport function convertHexToDecimal(h) {\n    return parseIntFromHex(h) / 255;\n}\n/** Parse a base-16 hex value into a base-10 integer */\nexport function parseIntFromHex(val) {\n    return parseInt(val, 16);\n}\nexport function numberInputToObject(color) {\n    return {\n        r: color >> 16,\n        g: (color & 0xff00) >> 8,\n        b: color & 0xff,\n    };\n}\n", "import { cmykToRgb, convertHexToDecimal, hslToRgb, hsvToRgb, parseIntFromHex, rgbToRgb, } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { boundAlpha, convertToPercentage } from './util.js';\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * \"cmyk(0, 20, 0, 0)\" or \"cmyk 0 20 0 0\"\n * ```\n */\nexport function inputToRGB(color) {\n    let rgb = { r: 0, g: 0, b: 0 };\n    let a = 1;\n    let s = null;\n    let v = null;\n    let l = null;\n    let ok = false;\n    let format = false;\n    if (typeof color === 'string') {\n        color = stringInputToObject(color);\n    }\n    if (typeof color === 'object') {\n        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n            rgb = rgbToRgb(color.r, color.g, color.b);\n            ok = true;\n            format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n            s = convertToPercentage(color.s);\n            v = convertToPercentage(color.v);\n            rgb = hsvToRgb(color.h, s, v);\n            ok = true;\n            format = 'hsv';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n            s = convertToPercentage(color.s);\n            l = convertToPercentage(color.l);\n            rgb = hslToRgb(color.h, s, l);\n            ok = true;\n            format = 'hsl';\n        }\n        else if (isValidCSSUnit(color.c) &&\n            isValidCSSUnit(color.m) &&\n            isValidCSSUnit(color.y) &&\n            isValidCSSUnit(color.k)) {\n            rgb = cmykToRgb(color.c, color.m, color.y, color.k);\n            ok = true;\n            format = 'cmyk';\n        }\n        if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n            a = color.a;\n        }\n    }\n    a = boundAlpha(a);\n    return {\n        ok,\n        format: color.format || format,\n        r: Math.min(255, Math.max(rgb.r, 0)),\n        g: Math.min(255, Math.max(rgb.g, 0)),\n        b: Math.min(255, Math.max(rgb.b, 0)),\n        a,\n    };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nconst CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nconst CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nconst CSS_UNIT = '(?:' + CSS_NUMBER + ')|(?:' + CSS_INTEGER + ')';\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\n// eslint-disable-next-line prettier/prettier\nconst PERMISSIVE_MATCH3 = '[\\\\s|\\\\(]+(' + CSS_UNIT + ')[,|\\\\s]+(' + CSS_UNIT + ')[,|\\\\s]+(' + CSS_UNIT + ')\\\\s*\\\\)?';\nconst PERMISSIVE_MATCH4 = \n// eslint-disable-next-line prettier/prettier\n'[\\\\s|\\\\(]+(' + CSS_UNIT + ')[,|\\\\s]+(' + CSS_UNIT + ')[,|\\\\s]+(' + CSS_UNIT + ')[,|\\\\s]+(' + CSS_UNIT + ')\\\\s*\\\\)?';\nconst matchers = {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n    rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n    hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n    hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n    hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n    hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n    cmyk: new RegExp('cmyk' + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}` or `{c, m, y, k}` or `{c, m, y, k, a}`\n */\nexport function stringInputToObject(color) {\n    color = color.trim().toLowerCase();\n    if (color.length === 0) {\n        return false;\n    }\n    let named = false;\n    if (names[color]) {\n        color = names[color];\n        named = true;\n    }\n    else if (color === 'transparent') {\n        return { r: 0, g: 0, b: 0, a: 0, format: 'name' };\n    }\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    let match = matchers.rgb.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3] };\n    }\n    match = matchers.rgba.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3], a: match[4] };\n    }\n    match = matchers.hsl.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3] };\n    }\n    match = matchers.hsla.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3], a: match[4] };\n    }\n    match = matchers.hsv.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3] };\n    }\n    match = matchers.hsva.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3], a: match[4] };\n    }\n    match = matchers.cmyk.exec(color);\n    if (match) {\n        return {\n            c: match[1],\n            m: match[2],\n            y: match[3],\n            k: match[4],\n        };\n    }\n    match = matchers.hex8.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            a: convertHexToDecimal(match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex6.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    match = matchers.hex4.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            a: convertHexToDecimal(match[4] + match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex3.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nexport function isValidCSSUnit(color) {\n    if (typeof color === 'number') {\n        return !Number.isNaN(color);\n    }\n    return matchers.CSS_UNIT.test(color);\n}\n", "import { numberInputToObject, rgbaToHex, rgbToCmyk, rgbToHex, rgbToHsl, rgbToHsv, } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input.js';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nexport class TinyColor {\n    constructor(color = '', opts = {}) {\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === 'number') {\n            color = numberInputToObject(color);\n        }\n        this.originalInput = color;\n        const rgb = inputToRGB(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = opts.format ?? rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    isDark() {\n        return this.getBrightness() < 128;\n    }\n    isLight() {\n        return !this.isDark();\n    }\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */\n    getBrightness() {\n        // http://www.w3.org/TR/AERT#color-contrast\n        const rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    }\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */\n    getLuminance() {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        const rgb = this.toRgb();\n        let R;\n        let G;\n        let B;\n        const RsRGB = rgb.r / 255;\n        const GsRGB = rgb.g / 255;\n        const BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    }\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */\n    getAlpha() {\n        return this.a;\n    }\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */\n    setAlpha(alpha) {\n        this.a = boundAlpha(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    }\n    /**\n     * Returns whether the color is monochrome.\n     */\n    isMonochrome() {\n        const { s } = this.toHsl();\n        return s === 0;\n    }\n    /**\n     * Returns the object as a HSVA object.\n     */\n    toHsv() {\n        const hsv = rgbToHsv(this.r, this.g, this.b);\n        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };\n    }\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */\n    toHsvString() {\n        const hsv = rgbToHsv(this.r, this.g, this.b);\n        const h = Math.round(hsv.h * 360);\n        const s = Math.round(hsv.s * 100);\n        const v = Math.round(hsv.v * 100);\n        return this.a === 1 ? `hsv(${h}, ${s}%, ${v}%)` : `hsva(${h}, ${s}%, ${v}%, ${this.roundA})`;\n    }\n    /**\n     * Returns the object as a HSLA object.\n     */\n    toHsl() {\n        const hsl = rgbToHsl(this.r, this.g, this.b);\n        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };\n    }\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */\n    toHslString() {\n        const hsl = rgbToHsl(this.r, this.g, this.b);\n        const h = Math.round(hsl.h * 360);\n        const s = Math.round(hsl.s * 100);\n        const l = Math.round(hsl.l * 100);\n        return this.a === 1 ? `hsl(${h}, ${s}%, ${l}%)` : `hsla(${h}, ${s}%, ${l}%, ${this.roundA})`;\n    }\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    toHex(allow3Char = false) {\n        return rgbToHex(this.r, this.g, this.b, allow3Char);\n    }\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    toHexString(allow3Char = false) {\n        return '#' + this.toHex(allow3Char);\n    }\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    toHex8(allow4Char = false) {\n        return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n    }\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    toHex8String(allow4Char = false) {\n        return '#' + this.toHex8(allow4Char);\n    }\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */\n    toHexShortString(allowShortChar = false) {\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    }\n    /**\n     * Returns the object as a RGBA object.\n     */\n    toRgb() {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a,\n        };\n    }\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */\n    toRgbString() {\n        const r = Math.round(this.r);\n        const g = Math.round(this.g);\n        const b = Math.round(this.b);\n        return this.a === 1 ? `rgb(${r}, ${g}, ${b})` : `rgba(${r}, ${g}, ${b}, ${this.roundA})`;\n    }\n    /**\n     * Returns the object as a RGBA object.\n     */\n    toPercentageRgb() {\n        const fmt = (x) => `${Math.round(bound01(x, 255) * 100)}%`;\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a,\n        };\n    }\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */\n    toPercentageRgbString() {\n        const rnd = (x) => Math.round(bound01(x, 255) * 100);\n        return this.a === 1\n            ? `rgb(${rnd(this.r)}%, ${rnd(this.g)}%, ${rnd(this.b)}%)`\n            : `rgba(${rnd(this.r)}%, ${rnd(this.g)}%, ${rnd(this.b)}%, ${this.roundA})`;\n    }\n    toCmyk() {\n        return {\n            ...rgbToCmyk(this.r, this.g, this.b),\n        };\n    }\n    toCmykString() {\n        const { c, m, y, k } = rgbToCmyk(this.r, this.g, this.b);\n        return `cmyk(${c}, ${m}, ${y}, ${k})`;\n    }\n    /**\n     * The 'real' name of the color -if there is one.\n     */\n    toName() {\n        if (this.a === 0) {\n            return 'transparent';\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        const hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n        for (const [key, value] of Object.entries(names)) {\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    }\n    toString(format) {\n        const formatSet = Boolean(format);\n        format = format ?? this.format;\n        let formattedString = false;\n        const hasAlpha = this.a < 1 && this.a >= 0;\n        const needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === 'name' && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === 'rgb') {\n            formattedString = this.toRgbString();\n        }\n        if (format === 'prgb') {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === 'hex' || format === 'hex6') {\n            formattedString = this.toHexString();\n        }\n        if (format === 'hex3') {\n            formattedString = this.toHexString(true);\n        }\n        if (format === 'hex4') {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === 'hex8') {\n            formattedString = this.toHex8String();\n        }\n        if (format === 'name') {\n            formattedString = this.toName();\n        }\n        if (format === 'hsl') {\n            formattedString = this.toHslString();\n        }\n        if (format === 'hsv') {\n            formattedString = this.toHsvString();\n        }\n        if (format === 'cmyk') {\n            formattedString = this.toCmykString();\n        }\n        return formattedString || this.toHexString();\n    }\n    toNumber() {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    }\n    clone() {\n        return new TinyColor(this.toString());\n    }\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    lighten(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    brighten(amount = 10) {\n        const rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    }\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    darken(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    tint(amount = 10) {\n        return this.mix('white', amount);\n    }\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    shade(amount = 10) {\n        return this.mix('black', amount);\n    }\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */\n    desaturate(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    saturate(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */\n    greyscale() {\n        return this.desaturate(100);\n    }\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */\n    spin(amount) {\n        const hsl = this.toHsl();\n        const hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    }\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */\n    mix(color, amount = 50) {\n        const rgb1 = this.toRgb();\n        const rgb2 = new TinyColor(color).toRgb();\n        const p = amount / 100;\n        const rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a,\n        };\n        return new TinyColor(rgba);\n    }\n    analogous(results = 6, slices = 30) {\n        const hsl = this.toHsl();\n        const part = 360 / slices;\n        const ret = [this];\n        for (hsl.h = (hsl.h - ((part * results) >> 1) + 720) % 360; --results;) {\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    }\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */\n    complement() {\n        const hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    }\n    monochromatic(results = 6) {\n        const hsv = this.toHsv();\n        const { h } = hsv;\n        const { s } = hsv;\n        let { v } = hsv;\n        const res = [];\n        const modification = 1 / results;\n        while (results--) {\n            res.push(new TinyColor({ h, s, v }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    }\n    splitcomplement() {\n        const hsl = this.toHsl();\n        const { h } = hsl;\n        return [\n            this,\n            new TinyColor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),\n            new TinyColor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l }),\n        ];\n    }\n    /**\n     * Compute how the color would appear on a background\n     */\n    onBackground(background) {\n        const fg = this.toRgb();\n        const bg = new TinyColor(background).toRgb();\n        const alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha,\n        });\n    }\n    /**\n     * Alias for `polyad(3)`\n     */\n    triad() {\n        return this.polyad(3);\n    }\n    /**\n     * Alias for `polyad(4)`\n     */\n    tetrad() {\n        return this.polyad(4);\n    }\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */\n    polyad(n) {\n        const hsl = this.toHsl();\n        const { h } = hsl;\n        const result = [this];\n        const increment = 360 / n;\n        for (let i = 1; i < n; i++) {\n            result.push(new TinyColor({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));\n        }\n        return result;\n    }\n    /**\n     * compare color vs current color\n     */\n    equals(color) {\n        const comparedColor = new TinyColor(color);\n        /**\n         * RGB and CMYK do not have the same color gamut, so a CMYK conversion will never be 100%.\n         * This means we need to compare CMYK to CMYK to ensure accuracy of the equals function.\n         */\n        if (this.format === 'cmyk' || comparedColor.format === 'cmyk') {\n            return this.toCmykString() === comparedColor.toCmykString();\n        }\n        return this.toRgbString() === comparedColor.toRgbString();\n    }\n}\n", "// randomColor by <PERSON> under the CC0 license\n// https://github.com/davidmerfield/randomColor/\nimport { TinyColor } from './index.js';\nexport function random(options = {}) {\n    // Check if we need to generate multiple colors\n    if (options.count !== undefined &&\n        options.count !== null) {\n        const totalColors = options.count;\n        const colors = [];\n        options.count = undefined;\n        while (totalColors > colors.length) {\n            // Since we're generating multiple colors,\n            // incremement the seed. Otherwise we'd just\n            // generate the same color each time...\n            options.count = null;\n            if (options.seed) {\n                options.seed += 1;\n            }\n            colors.push(random(options));\n        }\n        options.count = totalColors;\n        return colors;\n    }\n    // First we pick a hue (H)\n    const h = pickHue(options.hue, options.seed);\n    // Then use H to determine saturation (S)\n    const s = pickSaturation(h, options);\n    // Then use S and H to determine brightness (B).\n    const v = pickBrightness(h, s, options);\n    const res = { h, s, v };\n    if (options.alpha !== undefined) {\n        res.a = options.alpha;\n    }\n    // Then we return the HSB color in the desired format\n    return new TinyColor(res);\n}\nfunction pickHue(hue, seed) {\n    const hueRange = getHueRange(hue);\n    let res = randomWithin(hueRange, seed);\n    // Instead of storing red as two seperate ranges,\n    // we group them, using negative numbers\n    if (res < 0) {\n        res = 360 + res;\n    }\n    return res;\n}\nfunction pickSaturation(hue, options) {\n    if (options.hue === 'monochrome') {\n        return 0;\n    }\n    if (options.luminosity === 'random') {\n        return randomWithin([0, 100], options.seed);\n    }\n    const { saturationRange } = getColorInfo(hue);\n    let sMin = saturationRange[0];\n    let sMax = saturationRange[1];\n    switch (options.luminosity) {\n        case 'bright':\n            sMin = 55;\n            break;\n        case 'dark':\n            sMin = sMax - 10;\n            break;\n        case 'light':\n            sMax = 55;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([sMin, sMax], options.seed);\n}\nfunction pickBrightness(H, S, options) {\n    let bMin = getMinimumBrightness(H, S);\n    let bMax = 100;\n    switch (options.luminosity) {\n        case 'dark':\n            bMax = bMin + 20;\n            break;\n        case 'light':\n            bMin = (bMax + bMin) / 2;\n            break;\n        case 'random':\n            bMin = 0;\n            bMax = 100;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([bMin, bMax], options.seed);\n}\nfunction getMinimumBrightness(H, S) {\n    const { lowerBounds } = getColorInfo(H);\n    for (let i = 0; i < lowerBounds.length - 1; i++) {\n        const s1 = lowerBounds[i][0];\n        const v1 = lowerBounds[i][1];\n        const s2 = lowerBounds[i + 1][0];\n        const v2 = lowerBounds[i + 1][1];\n        if (S >= s1 && S <= s2) {\n            const m = (v2 - v1) / (s2 - s1);\n            const b = v1 - m * s1;\n            return m * S + b;\n        }\n    }\n    return 0;\n}\nfunction getHueRange(colorInput) {\n    const num = parseInt(colorInput, 10);\n    if (!Number.isNaN(num) && num < 360 && num > 0) {\n        return [num, num];\n    }\n    if (typeof colorInput === 'string') {\n        const namedColor = bounds.find(n => n.name === colorInput);\n        if (namedColor) {\n            const color = defineColor(namedColor);\n            if (color.hueRange) {\n                return color.hueRange;\n            }\n        }\n        const parsed = new TinyColor(colorInput);\n        if (parsed.isValid) {\n            const hue = parsed.toHsv().h;\n            return [hue, hue];\n        }\n    }\n    return [0, 360];\n}\nfunction getColorInfo(hue) {\n    // Maps red colors to make picking hue easier\n    if (hue >= 334 && hue <= 360) {\n        hue -= 360;\n    }\n    for (const bound of bounds) {\n        const color = defineColor(bound);\n        if (color.hueRange && hue >= color.hueRange[0] && hue <= color.hueRange[1]) {\n            return color;\n        }\n    }\n    throw Error('Color not found');\n}\nfunction randomWithin(range, seed) {\n    if (seed === undefined) {\n        return Math.floor(range[0] + Math.random() * (range[1] + 1 - range[0]));\n    }\n    // Seeded random algorithm from http://indiegamr.com/generate-repeatable-random-numbers-in-js/\n    const max = range[1] || 1;\n    const min = range[0] || 0;\n    seed = (seed * 9301 + 49297) % 233280;\n    const rnd = seed / 233280.0;\n    return Math.floor(min + rnd * (max - min));\n}\nfunction defineColor(bound) {\n    const sMin = bound.lowerBounds[0][0];\n    const sMax = bound.lowerBounds[bound.lowerBounds.length - 1][0];\n    const bMin = bound.lowerBounds[bound.lowerBounds.length - 1][1];\n    const bMax = bound.lowerBounds[0][1];\n    return {\n        name: bound.name,\n        hueRange: bound.hueRange,\n        lowerBounds: bound.lowerBounds,\n        saturationRange: [sMin, sMax],\n        brightnessRange: [bMin, bMax],\n    };\n}\n/**\n * @hidden\n */\nexport const bounds = [\n    {\n        name: 'monochrome',\n        hueRange: null,\n        lowerBounds: [\n            [0, 0],\n            [100, 0],\n        ],\n    },\n    {\n        name: 'red',\n        hueRange: [-26, 18],\n        lowerBounds: [\n            [20, 100],\n            [30, 92],\n            [40, 89],\n            [50, 85],\n            [60, 78],\n            [70, 70],\n            [80, 60],\n            [90, 55],\n            [100, 50],\n        ],\n    },\n    {\n        name: 'orange',\n        hueRange: [19, 46],\n        lowerBounds: [\n            [20, 100],\n            [30, 93],\n            [40, 88],\n            [50, 86],\n            [60, 85],\n            [70, 70],\n            [100, 70],\n        ],\n    },\n    {\n        name: 'yellow',\n        hueRange: [47, 62],\n        lowerBounds: [\n            [25, 100],\n            [40, 94],\n            [50, 89],\n            [60, 86],\n            [70, 84],\n            [80, 82],\n            [90, 80],\n            [100, 75],\n        ],\n    },\n    {\n        name: 'green',\n        hueRange: [63, 178],\n        lowerBounds: [\n            [30, 100],\n            [40, 90],\n            [50, 85],\n            [60, 81],\n            [70, 74],\n            [80, 64],\n            [90, 50],\n            [100, 40],\n        ],\n    },\n    {\n        name: 'blue',\n        hueRange: [179, 257],\n        lowerBounds: [\n            [20, 100],\n            [30, 86],\n            [40, 80],\n            [50, 74],\n            [60, 60],\n            [70, 52],\n            [80, 44],\n            [90, 39],\n            [100, 35],\n        ],\n    },\n    {\n        name: 'purple',\n        hueRange: [258, 282],\n        lowerBounds: [\n            [20, 100],\n            [30, 87],\n            [40, 79],\n            [50, 70],\n            [60, 65],\n            [70, 59],\n            [80, 52],\n            [90, 45],\n            [100, 42],\n        ],\n    },\n    {\n        name: 'pink',\n        hueRange: [283, 334],\n        lowerBounds: [\n            [20, 100],\n            [30, 90],\n            [40, 86],\n            [60, 84],\n            [80, 80],\n            [90, 75],\n            [100, 73],\n        ],\n    },\n];\n", "import { TinyColor } from './index.js';\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nexport function readability(color1, color2) {\n    const c1 = new TinyColor(color1);\n    const c2 = new TinyColor(color2);\n    return ((Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) /\n        (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05));\n}\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nexport function isReadable(color1, color2, wcag2 = { level: 'AA', size: 'small' }) {\n    const readabilityLevel = readability(color1, color2);\n    switch ((wcag2.level ?? 'AA') + (wcag2.size ?? 'small')) {\n        case 'AAsmall':\n        case 'AAAlarge':\n            return readabilityLevel >= 4.5;\n        case 'AAlarge':\n            return readabilityLevel >= 3;\n        case 'AAAsmall':\n            return readabilityLevel >= 7;\n        default:\n            return false;\n    }\n}\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nexport function mostReadable(baseColor, colorList, args = { includeFallbackColors: false, level: 'AA', size: 'small' }) {\n    let bestColor = null;\n    let bestScore = 0;\n    const { includeFallbackColors, level, size } = args;\n    for (const color of colorList) {\n        const score = readability(baseColor, color);\n        if (score > bestScore) {\n            bestScore = score;\n            bestColor = new TinyColor(color);\n        }\n    }\n    if (isReadable(baseColor, bestColor, { level, size }) || !includeFallbackColors) {\n        return bestColor;\n    }\n    args.includeFallbackColors = false;\n    return mostReadable(baseColor, ['#fff', '#000'], args);\n}\n", "import { names } from './css-color-names.js';\nimport { inputToRGB, isValidCSSUnit, stringInputToObject } from './format-input.js';\nimport { fromRatio, legacyRandom } from './from-ratio.js';\nimport { TinyColor } from './index.js';\nimport { random } from './random.js';\nimport { mostReadable, readability } from './readability.js';\nimport { toMsFilter } from './to-ms-filter.js';\nconst tinycolorumd = {\n    TinyColor,\n    readability,\n    mostReadable,\n    random,\n    names,\n    fromRatio,\n    legacyRandom,\n    toMsFilter,\n    inputToRGB,\n    stringInputToObject,\n    isValidCSSUnit,\n};\nexport default tinycolorumd;\n", "import { TinyColor } from './index.js';\nimport { convertToPercentage } from './util.js';\n/**\n * If input is an object, force 1 into \"1.0\" to handle ratios properly\n * String input requires \"1.0\" as input, so 1 will be treated as 1\n */\nexport function fromRatio(ratio, opts) {\n    const newColor = {\n        r: convertToPercentage(ratio.r),\n        g: convertToPercentage(ratio.g),\n        b: convertToPercentage(ratio.b),\n    };\n    if (ratio.a !== undefined) {\n        newColor.a = Number(ratio.a);\n    }\n    return new TinyColor(newColor, opts);\n}\n/** old random function */\nexport function legacyRandom() {\n    return new TinyColor({\n        r: Math.random(),\n        g: Math.random(),\n        b: Math.random(),\n    });\n}\n", "import { rgbaToArgbHex } from './conversion.js';\nimport { TinyColor } from './index.js';\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nexport function toMsFilter(firstColor, secondColor) {\n    const color = new TinyColor(firstColor);\n    const hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n    let secondHex8String = hex8String;\n    const gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n    if (secondColor) {\n        const s = new TinyColor(secondColor);\n        secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n    }\n    return `progid:DXImageTransform.Microsoft.gradient(${gradientType}startColorstr=${hex8String},endColorstr=${secondHex8String})`;\n}\n"], "names": ["names", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "goldenrod", "gold", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavenderblush", "lavender", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "bound01", "n", "max", "indexOf", "parseFloat", "isOnePointZero", "isPercent", "isPercentage", "Math", "min", "parseInt", "String", "abs", "clamp01", "val", "boundAlpha", "a", "isNaN", "convertToPercentage", "Number", "pad2", "c", "length", "rgbToHsl", "r", "g", "b", "h", "s", "l", "d", "hue2rgb", "p", "q", "t", "rgbToHsv", "v", "rgbToHex", "allow3Char", "hex", "round", "toString", "startsWith", "char<PERSON>t", "join", "rgbaToArgbHex", "convertDecimalToHex", "rgbToCmyk", "m", "y", "k", "convertHexToDecimal", "parseIntFromHex", "inputToRGB", "color", "rgb", "ok", "format", "stringInputToObject", "isValidCSSUnit", "substr", "i", "floor", "f", "mod", "hsvToRgb", "hslToRgb", "kConv", "cmykToRgb", "Object", "prototype", "hasOwnProperty", "call", "CSS_UNIT", "PERMISSIVE_MATCH3", "PERMISSIVE_MATCH4", "matchers", "RegExp", "rgba", "hsl", "hsla", "hsv", "hsva", "cmyk", "hex3", "hex6", "hex4", "hex8", "trim", "toLowerCase", "named", "match", "exec", "test", "TinyColor", "constructor", "opts", "numberInputToObject", "this", "originalInput", "roundA", "gradientType", "<PERSON><PERSON><PERSON><PERSON>", "isDark", "getBrightness", "isLight", "toRgb", "getLuminance", "R", "G", "B", "RsRGB", "GsRGB", "BsRGB", "pow", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "isMonochrome", "toHsl", "toHsv", "toHsvString", "toHslString", "toHex", "toHexString", "toHex8", "allow4Char", "rgbaToHex", "toHex8String", "toHexShortString", "allowShortChar", "toRgbString", "toPercentageRgb", "fmt", "x", "toPercentageRgbString", "rnd", "toCmyk", "toCmykString", "to<PERSON>ame", "key", "value", "entries", "formatSet", "Boolean", "formattedString", "has<PERSON><PERSON><PERSON>", "toNumber", "clone", "lighten", "amount", "brighten", "darken", "tint", "mix", "shade", "desaturate", "saturate", "greyscale", "spin", "hue", "rgb1", "rgb2", "analogous", "results", "slices", "part", "ret", "push", "complement", "monochromatic", "res", "modification", "splitcomplement", "onBackground", "background", "fg", "bg", "triad", "polyad", "tetrad", "result", "increment", "equals", "comparedColor", "getColorInfo", "bound", "bounds", "defineColor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "randomWithin", "range", "seed", "undefined", "random", "sMin", "lowerBounds", "sMax", "bMin", "bMax", "name", "saturationRange", "brightnessRange", "readability", "color1", "color2", "c1", "c2", "tinycolorumd", "mostReadable", "baseColor", "colorList", "args", "includeFallbackColors", "level", "size", "bestColor", "bestScore", "score", "wcag2", "readabilityLevel", "isReadable", "options", "count", "totalColors", "colors", "colorInput", "num", "namedColor", "find", "parsed", "getHueRange", "pickHue", "luminosity", "pickSaturation", "H", "S", "s1", "v1", "s2", "v2", "getMinimumBrightness", "pickBrightness", "fromRatio", "ratio", "newColor", "legacyRandom", "to<PERSON><PERSON><PERSON><PERSON>", "firstColor", "secondColor", "hex8String", "secondHex8String"], "mappings": "0OAIO,MAAMA,EAAQ,CACjBC,UAAW,UACXC,aAAc,UACdC,KAAM,UACNC,WAAY,UACZC,MAAO,UACPC,MAAO,UACPC,OAAQ,UACRC,MAAO,UACPC,eAAgB,UAChBC,KAAM,UACNC,WAAY,UACZC,MAAO,UACPC,UAAW,UACXC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,MAAO,UACPC,eAAgB,UAChBC,SAAU,UACVC,QAAS,UACTC,KAAM,UACNC,SAAU,UACVC,SAAU,UACVC,cAAe,UACfC,SAAU,UACVC,UAAW,UACXC,SAAU,UACVC,UAAW,UACXC,YAAa,UACbC,eAAgB,UAChBC,WAAY,UACZC,WAAY,UACZC,QAAS,UACTC,WAAY,UACZC,aAAc,UACdC,cAAe,UACfC,cAAe,UACfC,cAAe,UACfC,cAAe,UACfC,WAAY,UACZC,SAAU,UACVC,YAAa,UACbC,QAAS,UACTC,QAAS,UACTC,WAAY,UACZC,UAAW,UACXC,YAAa,UACbC,YAAa,UACbC,QAAS,UACTC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,MAAO,UACPC,YAAa,UACbC,KAAM,UACNC,SAAU,UACVC,QAAS,UACTC,UAAW,UACXC,OAAQ,UACRC,MAAO,UACPC,MAAO,UACPC,cAAe,UACfC,SAAU,UACVC,UAAW,UACXC,aAAc,UACdC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,qBAAsB,UACtBC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,UAAW,UACXC,YAAa,UACbC,cAAe,UACfC,aAAc,UACdC,eAAgB,UAChBC,eAAgB,UAChBC,eAAgB,UAChBC,YAAa,UACbC,KAAM,UACNC,UAAW,UACXC,MAAO,UACPC,QAAS,UACTC,OAAQ,UACRC,iBAAkB,UAClBC,WAAY,UACZC,aAAc,UACdC,aAAc,UACdC,eAAgB,UAChBC,gBAAiB,UACjBC,kBAAmB,UACnBC,gBAAiB,UACjBC,gBAAiB,UACjBC,aAAc,UACdC,UAAW,UACXC,UAAW,UACXC,SAAU,UACVC,YAAa,UACbC,KAAM,UACNC,QAAS,UACTC,MAAO,UACPC,UAAW,UACXC,OAAQ,UACRC,UAAW,UACXC,OAAQ,UACRC,cAAe,UACfC,UAAW,UACXC,cAAe,UACfC,cAAe,UACfC,WAAY,UACZC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,KAAM,UACNC,WAAY,UACZC,OAAQ,UACRC,cAAe,UACfC,IAAK,UACLC,UAAW,UACXC,UAAW,UACXC,YAAa,UACbC,OAAQ,UACRC,WAAY,UACZC,SAAU,UACVC,SAAU,UACVC,OAAQ,UACRC,OAAQ,UACRC,QAAS,UACTC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,KAAM,UACNC,YAAa,UACbC,UAAW,UACXC,IAAK,UACLC,KAAM,UACNC,QAAS,UACTC,OAAQ,UACRC,UAAW,UACXC,OAAQ,UACRC,MAAO,UACPC,MAAO,UACPC,WAAY,UACZC,OAAQ,UACRC,YAAa,WCpJV,SAASC,EAAQC,EAAGC,IAwCpB,SAAwBD,GAC3B,MAAoB,iBAANA,IAAsC,IAApBA,EAAEE,QAAQ,MAAiC,IAAlBC,WAAWH,EACxE,EAzCQI,CAAeJ,KACfA,EAAI,QAER,MAAMK,EA2CH,SAAsBL,GACzB,MAAoB,iBAANA,IAAsC,IAApBA,EAAEE,QAAQ,IAC9C,CA7CsBI,CAAaN,GAO/B,OANAA,EAAY,MAARC,EAAcD,EAAIO,KAAKC,IAAIP,EAAKM,KAAKN,IAAI,EAAGE,WAAWH,KAEvDK,IACAL,EAAIS,SAASC,OAAOV,EAAIC,GAAM,IAAM,KAGpCM,KAAKI,IAAIX,EAAIC,GAAO,KACb,EAOPD,EAJQ,MAARC,GAIKD,EAAI,EAAKA,EAAIC,EAAOA,EAAMD,EAAIC,GAAOE,WAAWO,OAAOT,IAKvDD,EAAIC,EAAOE,WAAWO,OAAOT,GAG1C,CAKO,SAASW,EAAQC,GACpB,OAAON,KAAKC,IAAI,EAAGD,KAAKN,IAAI,EAAGY,GACnC,CAoBO,SAASC,EAAWC,GAKvB,OAJAA,EAAIZ,WAAWY,IACXC,MAAMD,IAAMA,EAAI,GAAKA,EAAI,KACzBA,EAAI,GAEDA,CACX,CAKO,SAASE,EAAoBjB,GAChC,OAAIkB,OAAOlB,IAAM,EACS,IAAZkB,OAAOlB,GAAV,IAEJA,CACX,CAKO,SAASmB,EAAKC,GACjB,OAAoB,IAAbA,EAAEC,OAAe,IAAMD,EAAIV,OAAOU,EAC7C,CC5DO,SAASE,EAASC,EAAGC,EAAGC,GAC3BF,EAAIxB,EAAQwB,EAAG,KACfC,EAAIzB,EAAQyB,EAAG,KACfC,EAAI1B,EAAQ0B,EAAG,KACf,MAAMxB,EAAMM,KAAKN,IAAIsB,EAAGC,EAAGC,GACrBjB,EAAMD,KAAKC,IAAIe,EAAGC,EAAGC,GAC3B,IAAIC,EAAI,EACJC,EAAI,EACR,MAAMC,GAAK3B,EAAMO,GAAO,EACxB,GAAIP,IAAQO,EACRmB,EAAI,EACJD,EAAI,MAEH,CACD,MAAMG,EAAI5B,EAAMO,EAEhB,OADAmB,EAAIC,EAAI,GAAMC,GAAK,EAAI5B,EAAMO,GAAOqB,GAAK5B,EAAMO,GACvCP,GACJ,KAAKsB,EACDG,GAAKF,EAAIC,GAAKI,GAAKL,EAAIC,EAAI,EAAI,GAC/B,MACJ,KAAKD,EACDE,GAAKD,EAAIF,GAAKM,EAAI,EAClB,MACJ,KAAKJ,EACDC,GAAKH,EAAIC,GAAKK,EAAI,EAK1BH,GAAK,CACR,CACD,MAAO,CAAEA,IAAGC,IAAGC,IACnB,CACA,SAASE,EAAQC,EAAGC,EAAGC,GAOnB,OANIA,EAAI,IACJA,GAAK,GAELA,EAAI,IACJA,GAAK,GAELA,EAAI,EAAI,EACDF,EAAe,EAAIE,GAAdD,EAAID,GAEhBE,EAAI,GACGD,EAEPC,EAAI,EAAI,EACDF,GAAKC,EAAID,IAAM,EAAI,EAAIE,GAAK,EAEhCF,CACX,CAmCO,SAASG,EAASX,EAAGC,EAAGC,GAC3BF,EAAIxB,EAAQwB,EAAG,KACfC,EAAIzB,EAAQyB,EAAG,KACfC,EAAI1B,EAAQ0B,EAAG,KACf,MAAMxB,EAAMM,KAAKN,IAAIsB,EAAGC,EAAGC,GACrBjB,EAAMD,KAAKC,IAAIe,EAAGC,EAAGC,GAC3B,IAAIC,EAAI,EACR,MAAMS,EAAIlC,EACJ4B,EAAI5B,EAAMO,EACVmB,EAAY,IAAR1B,EAAY,EAAI4B,EAAI5B,EAC9B,GAAIA,IAAQO,EACRkB,EAAI,MAEH,CACD,OAAQzB,GACJ,KAAKsB,EACDG,GAAKF,EAAIC,GAAKI,GAAKL,EAAIC,EAAI,EAAI,GAC/B,MACJ,KAAKD,EACDE,GAAKD,EAAIF,GAAKM,EAAI,EAClB,MACJ,KAAKJ,EACDC,GAAKH,EAAIC,GAAKK,EAAI,EAK1BH,GAAK,CACR,CACD,MAAO,CAAEA,IAAGC,IAAGQ,IACnB,CA4BO,SAASC,EAASb,EAAGC,EAAGC,EAAGY,GAC9B,MAAMC,EAAM,CACRnB,EAAKZ,KAAKgC,MAAMhB,GAAGiB,SAAS,KAC5BrB,EAAKZ,KAAKgC,MAAMf,GAAGgB,SAAS,KAC5BrB,EAAKZ,KAAKgC,MAAMd,GAAGe,SAAS,MAGhC,OAAIH,GACAC,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,KAChCJ,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,KAChCJ,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,IACzBJ,EAAI,GAAGI,OAAO,GAAKJ,EAAI,GAAGI,OAAO,GAAKJ,EAAI,GAAGI,OAAO,GAExDJ,EAAIK,KAAK,GACpB,CAgCO,SAASC,EAAcrB,EAAGC,EAAGC,EAAGV,GAOnC,MANY,CACRI,EAAK0B,EAAoB9B,IACzBI,EAAKZ,KAAKgC,MAAMhB,GAAGiB,SAAS,KAC5BrB,EAAKZ,KAAKgC,MAAMf,GAAGgB,SAAS,KAC5BrB,EAAKZ,KAAKgC,MAAMd,GAAGe,SAAS,MAErBG,KAAK,GACpB,CAeO,SAASG,EAAUvB,EAAGC,EAAGC,GAC5B,IAAIL,EAAI,EAAIG,EAAI,IACZwB,EAAI,EAAIvB,EAAI,IACZwB,EAAI,EAAIvB,EAAI,IACZwB,EAAI1C,KAAKC,IAAIY,EAAG2B,EAAGC,GAYvB,OAXU,IAANC,GACA7B,EAAI,EACJ2B,EAAI,EACJC,EAAI,IAGJ5B,GAAMA,EAAI6B,IAAM,EAAIA,GAAM,IAC1BF,GAAMA,EAAIE,IAAM,EAAIA,GAAM,IAC1BD,GAAMA,EAAIC,IAAM,EAAIA,GAAM,KAE9BA,GAAK,IACE,CACH7B,EAAGb,KAAKgC,MAAMnB,GACd2B,EAAGxC,KAAKgC,MAAMQ,GACdC,EAAGzC,KAAKgC,MAAMS,GACdC,EAAG1C,KAAKgC,MAAMU,GAEtB,CAEO,SAASJ,EAAoBhB,GAChC,OAAOtB,KAAKgC,MAAsB,IAAhBpC,WAAW0B,IAAUW,SAAS,GACpD,CAEO,SAASU,EAAoBxB,GAChC,OAAOyB,EAAgBzB,GAAK,GAChC,CAEO,SAASyB,EAAgBtC,GAC5B,OAAOJ,SAASI,EAAK,GACzB,CCrPO,SAASuC,EAAWC,GACvB,IAAIC,EAAM,CAAE/B,EAAG,EAAGC,EAAG,EAAGC,EAAG,GACvBV,EAAI,EACJY,EAAI,KACJQ,EAAI,KACJP,EAAI,KACJ2B,GAAK,EACLC,GAAS,EDpBV,IAAkBjC,EAAGC,EAAGC,ECyD3B,MApCqB,iBAAV4B,IACPA,EAAQI,EAAoBJ,IAEX,iBAAVA,IACHK,EAAeL,EAAM9B,IAAMmC,EAAeL,EAAM7B,IAAMkC,EAAeL,EAAM5B,IDzB9DF,EC0BE8B,EAAM9B,ED1BLC,EC0BQ6B,EAAM7B,ED1BXC,EC0Bc4B,EAAM5B,EAAvC6B,EDzBD,CACH/B,EAAqB,IAAlBxB,EAAQwB,EAAG,KACdC,EAAqB,IAAlBzB,EAAQyB,EAAG,KACdC,EAAqB,IAAlB1B,EAAQ0B,EAAG,MCuBV8B,GAAK,EACLC,EAAwC,MAA/B9C,OAAO2C,EAAM9B,GAAGoC,QAAQ,GAAa,OAAS,OAElDD,EAAeL,EAAM3B,IAAMgC,EAAeL,EAAM1B,IAAM+B,EAAeL,EAAMlB,IAChFR,EAAIV,EAAoBoC,EAAM1B,GAC9BQ,EAAIlB,EAAoBoC,EAAMlB,GAC9BmB,EDqGL,SAAkB5B,EAAGC,EAAGQ,GAC3BT,EAAsB,EAAlB3B,EAAQ2B,EAAG,KACfC,EAAI5B,EAAQ4B,EAAG,KACfQ,EAAIpC,EAAQoC,EAAG,KACf,MAAMyB,EAAIrD,KAAKsD,MAAMnC,GACfoC,EAAIpC,EAAIkC,EACR7B,EAAII,GAAK,EAAIR,GACbK,EAAIG,GAAK,EAAI2B,EAAInC,GACjBM,EAAIE,GAAK,GAAK,EAAI2B,GAAKnC,GACvBoC,EAAMH,EAAI,EAIhB,MAAO,CAAErC,EAAO,IAHN,CAACY,EAAGH,EAAGD,EAAGA,EAAGE,EAAGE,GAAG4B,GAGRvC,EAAO,IAFlB,CAACS,EAAGE,EAAGA,EAAGH,EAAGD,EAAGA,GAAGgC,GAEItC,EAAO,IAD9B,CAACM,EAAGA,EAAGE,EAAGE,EAAGA,EAAGH,GAAG+B,GAEjC,CCnHkBC,CAASX,EAAM3B,EAAGC,EAAGQ,GAC3BoB,GAAK,EACLC,EAAS,OAEJE,EAAeL,EAAM3B,IAAMgC,EAAeL,EAAM1B,IAAM+B,EAAeL,EAAMzB,IAChFD,EAAIV,EAAoBoC,EAAM1B,GAC9BC,EAAIX,EAAoBoC,EAAMzB,GAC9B0B,ED6BL,SAAkB5B,EAAGC,EAAGC,GAC3B,IAAIL,EACAC,EACAC,EAIJ,GAHAC,EAAI3B,EAAQ2B,EAAG,KACfC,EAAI5B,EAAQ4B,EAAG,KACfC,EAAI7B,EAAQ6B,EAAG,KACL,IAAND,EAEAH,EAAII,EACJH,EAAIG,EACJL,EAAIK,MAEH,CACD,MAAMI,EAAIJ,EAAI,GAAMA,GAAK,EAAID,GAAKC,EAAID,EAAIC,EAAID,EACxCI,EAAI,EAAIH,EAAII,EAClBT,EAAIO,EAAQC,EAAGC,EAAGN,EAAI,EAAI,GAC1BF,EAAIM,EAAQC,EAAGC,EAAGN,GAClBD,EAAIK,EAAQC,EAAGC,EAAGN,EAAI,EAAI,EAC7B,CACD,MAAO,CAAEH,EAAO,IAAJA,EAASC,EAAO,IAAJA,EAASC,EAAO,IAAJA,EACxC,CClDkBwC,CAASZ,EAAM3B,EAAGC,EAAGC,GAC3B2B,GAAK,EACLC,EAAS,OAEJE,EAAeL,EAAMjC,IAC1BsC,EAAeL,EAAMN,IACrBW,EAAeL,EAAML,IACrBU,EAAeL,EAAMJ,KACrBK,EDsKL,SAAmBlC,EAAG2B,EAAGC,EAAGC,GAC/B,MAGMiB,EAAQjB,EAAI,IAIlB,MAAO,CAAE1B,EAHC,KAAO,EAJHH,EAAI,MAIa,EAAI8C,GAGvB1C,EAFF,KAAO,EAJHuB,EAAI,MAIa,EAAImB,GAEpBzC,EADL,KAAO,EAJHuB,EAAI,MAIa,EAAIkB,GAEvC,CC/KkBC,CAAUd,EAAMjC,EAAGiC,EAAMN,EAAGM,EAAML,EAAGK,EAAMJ,GACjDM,GAAK,EACLC,EAAS,QAETY,OAAOC,UAAUC,eAAeC,KAAKlB,EAAO,OAC5CtC,EAAIsC,EAAMtC,IAGlBA,EAAID,EAAWC,GACR,CACHwC,KACAC,OAAQH,EAAMG,QAAUA,EACxBjC,EAAGhB,KAAKC,IAAI,IAAKD,KAAKN,IAAIqD,EAAI/B,EAAG,IACjCC,EAAGjB,KAAKC,IAAI,IAAKD,KAAKN,IAAIqD,EAAI9B,EAAG,IACjCC,EAAGlB,KAAKC,IAAI,IAAKD,KAAKN,IAAIqD,EAAI7B,EAAG,IACjCV,IAER,CAEA,MAIMyD,EAAW,6CAKXC,EAAoB,cAAgBD,EAAW,aAAeA,EAAW,aAAeA,EAAW,YACnGE,EAEN,cAAgBF,EAAW,aAAeA,EAAW,aAAeA,EAAW,aAAeA,EAAW,YACnGG,EAAW,CACbH,SAAU,IAAII,OAAOJ,GACrBlB,IAAK,IAAIsB,OAAO,MAAQH,GACxBI,KAAM,IAAID,OAAO,OAASF,GAC1BI,IAAK,IAAIF,OAAO,MAAQH,GACxBM,KAAM,IAAIH,OAAO,OAASF,GAC1BM,IAAK,IAAIJ,OAAO,MAAQH,GACxBQ,KAAM,IAAIL,OAAO,OAASF,GAC1BQ,KAAM,IAAIN,OAAO,OAASF,GAC1BS,KAAM,uDACNC,KAAM,uDACNC,KAAM,uEACNC,KAAM,wEAMH,SAAS7B,EAAoBJ,GAEhC,GAAqB,KADrBA,EAAQA,EAAMkC,OAAOC,eACXnE,OACN,OAAO,EAEX,IAAIoE,GAAQ,EACZ,GAAI/O,EAAM2M,GACNA,EAAQ3M,EAAM2M,GACdoC,GAAQ,OAEP,GAAc,gBAAVpC,EACL,MAAO,CAAE9B,EAAG,EAAGC,EAAG,EAAGC,EAAG,EAAGV,EAAG,EAAGyC,OAAQ,QAM7C,IAAIkC,EAAQf,EAASrB,IAAIqC,KAAKtC,GAC9B,OAAIqC,EACO,CAAEnE,EAAGmE,EAAM,GAAIlE,EAAGkE,EAAM,GAAIjE,EAAGiE,EAAM,KAEhDA,EAAQf,EAASE,KAAKc,KAAKtC,GACvBqC,EACO,CAAEnE,EAAGmE,EAAM,GAAIlE,EAAGkE,EAAM,GAAIjE,EAAGiE,EAAM,GAAI3E,EAAG2E,EAAM,KAE7DA,EAAQf,EAASG,IAAIa,KAAKtC,GACtBqC,EACO,CAAEhE,EAAGgE,EAAM,GAAI/D,EAAG+D,EAAM,GAAI9D,EAAG8D,EAAM,KAEhDA,EAAQf,EAASI,KAAKY,KAAKtC,GACvBqC,EACO,CAAEhE,EAAGgE,EAAM,GAAI/D,EAAG+D,EAAM,GAAI9D,EAAG8D,EAAM,GAAI3E,EAAG2E,EAAM,KAE7DA,EAAQf,EAASK,IAAIW,KAAKtC,GACtBqC,EACO,CAAEhE,EAAGgE,EAAM,GAAI/D,EAAG+D,EAAM,GAAIvD,EAAGuD,EAAM,KAEhDA,EAAQf,EAASM,KAAKU,KAAKtC,GACvBqC,EACO,CAAEhE,EAAGgE,EAAM,GAAI/D,EAAG+D,EAAM,GAAIvD,EAAGuD,EAAM,GAAI3E,EAAG2E,EAAM,KAE7DA,EAAQf,EAASO,KAAKS,KAAKtC,GACvBqC,EACO,CACHtE,EAAGsE,EAAM,GACT3C,EAAG2C,EAAM,GACT1C,EAAG0C,EAAM,GACTzC,EAAGyC,EAAM,KAGjBA,EAAQf,EAASW,KAAKK,KAAKtC,GACvBqC,EACO,CACHnE,EAAG4B,EAAgBuC,EAAM,IACzBlE,EAAG2B,EAAgBuC,EAAM,IACzBjE,EAAG0B,EAAgBuC,EAAM,IACzB3E,EAAGmC,EAAoBwC,EAAM,IAC7BlC,OAAQiC,EAAQ,OAAS,SAGjCC,EAAQf,EAASS,KAAKO,KAAKtC,GACvBqC,EACO,CACHnE,EAAG4B,EAAgBuC,EAAM,IACzBlE,EAAG2B,EAAgBuC,EAAM,IACzBjE,EAAG0B,EAAgBuC,EAAM,IACzBlC,OAAQiC,EAAQ,OAAS,QAGjCC,EAAQf,EAASU,KAAKM,KAAKtC,GACvBqC,EACO,CACHnE,EAAG4B,EAAgBuC,EAAM,GAAKA,EAAM,IACpClE,EAAG2B,EAAgBuC,EAAM,GAAKA,EAAM,IACpCjE,EAAG0B,EAAgBuC,EAAM,GAAKA,EAAM,IACpC3E,EAAGmC,EAAoBwC,EAAM,GAAKA,EAAM,IACxClC,OAAQiC,EAAQ,OAAS,SAGjCC,EAAQf,EAASQ,KAAKQ,KAAKtC,KACvBqC,GACO,CACHnE,EAAG4B,EAAgBuC,EAAM,GAAKA,EAAM,IACpClE,EAAG2B,EAAgBuC,EAAM,GAAKA,EAAM,IACpCjE,EAAG0B,EAAgBuC,EAAM,GAAKA,EAAM,IACpClC,OAAQiC,EAAQ,OAAS,gBAIrC,CAKO,SAAS/B,EAAeL,GAC3B,MAAqB,iBAAVA,GACCnC,OAAOF,MAAMqC,GAElBsB,EAASH,SAASoB,KAAKvC,EAClC,CC1MO,MAAMwC,EACT,WAAAC,CAAYzC,EAAQ,GAAI0C,EAAO,CAAA,GAE3B,GAAI1C,aAAiBwC,EAEjB,OAAOxC,EAEU,iBAAVA,IACPA,EFgQL,SAA6BA,GAChC,MAAO,CACH9B,EAAG8B,GAAS,GACZ7B,GAAY,MAAR6B,IAAmB,EACvB5B,EAAW,IAAR4B,EAEX,CEtQoB2C,CAAoB3C,IAEhC4C,KAAKC,cAAgB7C,EACrB,MAAMC,EAAMF,EAAWC,GACvB4C,KAAKC,cAAgB7C,EACrB4C,KAAK1E,EAAI+B,EAAI/B,EACb0E,KAAKzE,EAAI8B,EAAI9B,EACbyE,KAAKxE,EAAI6B,EAAI7B,EACbwE,KAAKlF,EAAIuC,EAAIvC,EACbkF,KAAKE,OAAS5F,KAAKgC,MAAM,IAAM0D,KAAKlF,GAAK,IACzCkF,KAAKzC,OAASuC,EAAKvC,QAAUF,EAAIE,OACjCyC,KAAKG,aAAeL,EAAKK,aAKrBH,KAAK1E,EAAI,IACT0E,KAAK1E,EAAIhB,KAAKgC,MAAM0D,KAAK1E,IAEzB0E,KAAKzE,EAAI,IACTyE,KAAKzE,EAAIjB,KAAKgC,MAAM0D,KAAKzE,IAEzByE,KAAKxE,EAAI,IACTwE,KAAKxE,EAAIlB,KAAKgC,MAAM0D,KAAKxE,IAE7BwE,KAAKI,QAAU/C,EAAIC,EACtB,CACD,MAAA+C,GACI,OAAOL,KAAKM,gBAAkB,GACjC,CACD,OAAAC,GACI,OAAQP,KAAKK,QAChB,CAID,aAAAC,GAEI,MAAMjD,EAAM2C,KAAKQ,QACjB,OAAgB,IAARnD,EAAI/B,EAAkB,IAAR+B,EAAI9B,EAAkB,IAAR8B,EAAI7B,GAAW,GACtD,CAID,YAAAiF,GAEI,MAAMpD,EAAM2C,KAAKQ,QACjB,IAAIE,EACAC,EACAC,EACJ,MAAMC,EAAQxD,EAAI/B,EAAI,IAChBwF,EAAQzD,EAAI9B,EAAI,IAChBwF,EAAQ1D,EAAI7B,EAAI,IAsBtB,OApBIkF,EADAG,GAAS,OACLA,EAAQ,MAIRvG,KAAK0G,KAAKH,EAAQ,MAAS,MAAO,KAGtCF,EADAG,GAAS,OACLA,EAAQ,MAIRxG,KAAK0G,KAAKF,EAAQ,MAAS,MAAO,KAGtCF,EADAG,GAAS,OACLA,EAAQ,MAIRzG,KAAK0G,KAAKD,EAAQ,MAAS,MAAO,KAEnC,MAASL,EAAI,MAASC,EAAI,MAASC,CAC7C,CAID,QAAAK,GACI,OAAOjB,KAAKlF,CACf,CAMD,QAAAoG,CAASC,GAGL,OAFAnB,KAAKlF,EAAID,EAAWsG,GACpBnB,KAAKE,OAAS5F,KAAKgC,MAAM,IAAM0D,KAAKlF,GAAK,IAClCkF,IACV,CAID,YAAAoB,GACI,MAAM1F,EAAEA,GAAMsE,KAAKqB,QACnB,OAAa,IAAN3F,CACV,CAID,KAAA4F,GACI,MAAMvC,EAAM9C,EAAS+D,KAAK1E,EAAG0E,KAAKzE,EAAGyE,KAAKxE,GAC1C,MAAO,CAAEC,EAAW,IAARsD,EAAItD,EAASC,EAAGqD,EAAIrD,EAAGQ,EAAG6C,EAAI7C,EAAGpB,EAAGkF,KAAKlF,EACxD,CAKD,WAAAyG,GACI,MAAMxC,EAAM9C,EAAS+D,KAAK1E,EAAG0E,KAAKzE,EAAGyE,KAAKxE,GACpCC,EAAInB,KAAKgC,MAAc,IAARyC,EAAItD,GACnBC,EAAIpB,KAAKgC,MAAc,IAARyC,EAAIrD,GACnBQ,EAAI5B,KAAKgC,MAAc,IAARyC,EAAI7C,GACzB,OAAkB,IAAX8D,KAAKlF,EAAU,OAAOW,MAAMC,OAAOQ,MAAQ,QAAQT,MAAMC,OAAOQ,OAAO8D,KAAKE,SACtF,CAID,KAAAmB,GACI,MAAMxC,EAAMxD,EAAS2E,KAAK1E,EAAG0E,KAAKzE,EAAGyE,KAAKxE,GAC1C,MAAO,CAAEC,EAAW,IAARoD,EAAIpD,EAASC,EAAGmD,EAAInD,EAAGC,EAAGkD,EAAIlD,EAAGb,EAAGkF,KAAKlF,EACxD,CAKD,WAAA0G,GACI,MAAM3C,EAAMxD,EAAS2E,KAAK1E,EAAG0E,KAAKzE,EAAGyE,KAAKxE,GACpCC,EAAInB,KAAKgC,MAAc,IAARuC,EAAIpD,GACnBC,EAAIpB,KAAKgC,MAAc,IAARuC,EAAInD,GACnBC,EAAIrB,KAAKgC,MAAc,IAARuC,EAAIlD,GACzB,OAAkB,IAAXqE,KAAKlF,EAAU,OAAOW,MAAMC,OAAOC,MAAQ,QAAQF,MAAMC,OAAOC,OAAOqE,KAAKE,SACtF,CAKD,KAAAuB,CAAMrF,GAAa,GACf,OAAOD,EAAS6D,KAAK1E,EAAG0E,KAAKzE,EAAGyE,KAAKxE,EAAGY,EAC3C,CAKD,WAAAsF,CAAYtF,GAAa,GACrB,MAAO,IAAM4D,KAAKyB,MAAMrF,EAC3B,CAKD,MAAAuF,CAAOC,GAAa,GAChB,OFoBD,SAAmBtG,EAAGC,EAAGC,EAAGV,EAAG8G,GAClC,MAAMvF,EAAM,CACRnB,EAAKZ,KAAKgC,MAAMhB,GAAGiB,SAAS,KAC5BrB,EAAKZ,KAAKgC,MAAMf,GAAGgB,SAAS,KAC5BrB,EAAKZ,KAAKgC,MAAMd,GAAGe,SAAS,KAC5BrB,EAAK0B,EAAoB9B,KAG7B,OAAI8G,GACAvF,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,KAChCJ,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,KAChCJ,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,KAChCJ,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,IACzBJ,EAAI,GAAGI,OAAO,GAAKJ,EAAI,GAAGI,OAAO,GAAKJ,EAAI,GAAGI,OAAO,GAAKJ,EAAI,GAAGI,OAAO,GAE3EJ,EAAIK,KAAK,GACpB,CEpCemF,CAAU7B,KAAK1E,EAAG0E,KAAKzE,EAAGyE,KAAKxE,EAAGwE,KAAKlF,EAAG8G,EACpD,CAKD,YAAAE,CAAaF,GAAa,GACtB,MAAO,IAAM5B,KAAK2B,OAAOC,EAC5B,CAKD,gBAAAG,CAAiBC,GAAiB,GAC9B,OAAkB,IAAXhC,KAAKlF,EAAUkF,KAAK0B,YAAYM,GAAkBhC,KAAK8B,aAAaE,EAC9E,CAID,KAAAxB,GACI,MAAO,CACHlF,EAAGhB,KAAKgC,MAAM0D,KAAK1E,GACnBC,EAAGjB,KAAKgC,MAAM0D,KAAKzE,GACnBC,EAAGlB,KAAKgC,MAAM0D,KAAKxE,GACnBV,EAAGkF,KAAKlF,EAEf,CAKD,WAAAmH,GACI,MAAM3G,EAAIhB,KAAKgC,MAAM0D,KAAK1E,GACpBC,EAAIjB,KAAKgC,MAAM0D,KAAKzE,GACpBC,EAAIlB,KAAKgC,MAAM0D,KAAKxE,GAC1B,OAAkB,IAAXwE,KAAKlF,EAAU,OAAOQ,MAAMC,MAAMC,KAAO,QAAQF,MAAMC,MAAMC,MAAMwE,KAAKE,SAClF,CAID,eAAAgC,GACI,MAAMC,EAAOC,GAAM,GAAG9H,KAAKgC,MAAwB,IAAlBxC,EAAQsI,EAAG,SAC5C,MAAO,CACH9G,EAAG6G,EAAInC,KAAK1E,GACZC,EAAG4G,EAAInC,KAAKzE,GACZC,EAAG2G,EAAInC,KAAKxE,GACZV,EAAGkF,KAAKlF,EAEf,CAID,qBAAAuH,GACI,MAAMC,EAAOF,GAAM9H,KAAKgC,MAAwB,IAAlBxC,EAAQsI,EAAG,MACzC,OAAkB,IAAXpC,KAAKlF,EACN,OAAOwH,EAAItC,KAAK1E,QAAQgH,EAAItC,KAAKzE,QAAQ+G,EAAItC,KAAKxE,OAClD,QAAQ8G,EAAItC,KAAK1E,QAAQgH,EAAItC,KAAKzE,QAAQ+G,EAAItC,KAAKxE,QAAQwE,KAAKE,SACzE,CACD,MAAAqC,GACI,MAAO,IACA1F,EAAUmD,KAAK1E,EAAG0E,KAAKzE,EAAGyE,KAAKxE,GAEzC,CACD,YAAAgH,GACI,MAAMrH,EAAEA,EAAC2B,EAAEA,EAACC,EAAEA,EAACC,EAAEA,GAAMH,EAAUmD,KAAK1E,EAAG0E,KAAKzE,EAAGyE,KAAKxE,GACtD,MAAO,QAAQL,MAAM2B,MAAMC,MAAMC,IACpC,CAID,MAAAyF,GACI,GAAe,IAAXzC,KAAKlF,EACL,MAAO,cAEX,GAAIkF,KAAKlF,EAAI,EACT,OAAO,EAEX,MAAMuB,EAAM,IAAMF,EAAS6D,KAAK1E,EAAG0E,KAAKzE,EAAGyE,KAAKxE,GAAG,GACnD,IAAK,MAAOkH,EAAKC,KAAUxE,OAAOyE,QAAQnS,GACtC,GAAI4L,IAAQsG,EACR,OAAOD,EAGf,OAAO,CACV,CACD,QAAAnG,CAASgB,GACL,MAAMsF,EAAYC,QAAQvF,GAC1BA,EAASA,GAAUyC,KAAKzC,OACxB,IAAIwF,GAAkB,EACtB,MAAMC,EAAWhD,KAAKlF,EAAI,GAAKkF,KAAKlF,GAAK,EAEzC,OAD0B+H,IAAaG,IAAazF,EAAOf,WAAW,QAAqB,SAAXe,GASjE,QAAXA,IACAwF,EAAkB/C,KAAKiC,eAEZ,SAAX1E,IACAwF,EAAkB/C,KAAKqC,yBAEZ,QAAX9E,GAA+B,SAAXA,IACpBwF,EAAkB/C,KAAK0B,eAEZ,SAAXnE,IACAwF,EAAkB/C,KAAK0B,aAAY,IAExB,SAAXnE,IACAwF,EAAkB/C,KAAK8B,cAAa,IAEzB,SAAXvE,IACAwF,EAAkB/C,KAAK8B,gBAEZ,SAAXvE,IACAwF,EAAkB/C,KAAKyC,UAEZ,QAAXlF,IACAwF,EAAkB/C,KAAKwB,eAEZ,QAAXjE,IACAwF,EAAkB/C,KAAKuB,eAEZ,SAAXhE,IACAwF,EAAkB/C,KAAKwC,gBAEpBO,GAAmB/C,KAAK0B,eAnCZ,SAAXnE,GAAgC,IAAXyC,KAAKlF,EACnBkF,KAAKyC,SAETzC,KAAKiC,aAiCnB,CACD,QAAAgB,GACI,OAAQ3I,KAAKgC,MAAM0D,KAAK1E,IAAM,KAAOhB,KAAKgC,MAAM0D,KAAKzE,IAAM,GAAKjB,KAAKgC,MAAM0D,KAAKxE,EACnF,CACD,KAAA0H,GACI,OAAO,IAAItD,EAAUI,KAAKzD,WAC7B,CAKD,OAAA4G,CAAQC,EAAS,IACb,MAAMvE,EAAMmB,KAAKqB,QAGjB,OAFAxC,EAAIlD,GAAKyH,EAAS,IAClBvE,EAAIlD,EAAIhB,EAAQkE,EAAIlD,GACb,IAAIiE,EAAUf,EACxB,CAKD,QAAAwE,CAASD,EAAS,IACd,MAAM/F,EAAM2C,KAAKQ,QAIjB,OAHAnD,EAAI/B,EAAIhB,KAAKN,IAAI,EAAGM,KAAKC,IAAI,IAAK8C,EAAI/B,EAAIhB,KAAKgC,OAAc8G,EAAS,IAAjB,OACrD/F,EAAI9B,EAAIjB,KAAKN,IAAI,EAAGM,KAAKC,IAAI,IAAK8C,EAAI9B,EAAIjB,KAAKgC,OAAc8G,EAAS,IAAjB,OACrD/F,EAAI7B,EAAIlB,KAAKN,IAAI,EAAGM,KAAKC,IAAI,IAAK8C,EAAI7B,EAAIlB,KAAKgC,OAAc8G,EAAS,IAAjB,OAC9C,IAAIxD,EAAUvC,EACxB,CAMD,MAAAiG,CAAOF,EAAS,IACZ,MAAMvE,EAAMmB,KAAKqB,QAGjB,OAFAxC,EAAIlD,GAAKyH,EAAS,IAClBvE,EAAIlD,EAAIhB,EAAQkE,EAAIlD,GACb,IAAIiE,EAAUf,EACxB,CAMD,IAAA0E,CAAKH,EAAS,IACV,OAAOpD,KAAKwD,IAAI,QAASJ,EAC5B,CAMD,KAAAK,CAAML,EAAS,IACX,OAAOpD,KAAKwD,IAAI,QAASJ,EAC5B,CAMD,UAAAM,CAAWN,EAAS,IAChB,MAAMvE,EAAMmB,KAAKqB,QAGjB,OAFAxC,EAAInD,GAAK0H,EAAS,IAClBvE,EAAInD,EAAIf,EAAQkE,EAAInD,GACb,IAAIkE,EAAUf,EACxB,CAKD,QAAA8E,CAASP,EAAS,IACd,MAAMvE,EAAMmB,KAAKqB,QAGjB,OAFAxC,EAAInD,GAAK0H,EAAS,IAClBvE,EAAInD,EAAIf,EAAQkE,EAAInD,GACb,IAAIkE,EAAUf,EACxB,CAKD,SAAA+E,GACI,OAAO5D,KAAK0D,WAAW,IAC1B,CAKD,IAAAG,CAAKT,GACD,MAAMvE,EAAMmB,KAAKqB,QACXyC,GAAOjF,EAAIpD,EAAI2H,GAAU,IAE/B,OADAvE,EAAIpD,EAAIqI,EAAM,EAAI,IAAMA,EAAMA,EACvB,IAAIlE,EAAUf,EACxB,CAKD,GAAA2E,CAAIpG,EAAOgG,EAAS,IAChB,MAAMW,EAAO/D,KAAKQ,QACZwD,EAAO,IAAIpE,EAAUxC,GAAOoD,QAC5B1E,EAAIsH,EAAS,IACbxE,EAAO,CACTtD,GAAI0I,EAAK1I,EAAIyI,EAAKzI,GAAKQ,EAAIiI,EAAKzI,EAChCC,GAAIyI,EAAKzI,EAAIwI,EAAKxI,GAAKO,EAAIiI,EAAKxI,EAChCC,GAAIwI,EAAKxI,EAAIuI,EAAKvI,GAAKM,EAAIiI,EAAKvI,EAChCV,GAAIkJ,EAAKlJ,EAAIiJ,EAAKjJ,GAAKgB,EAAIiI,EAAKjJ,GAEpC,OAAO,IAAI8E,EAAUhB,EACxB,CACD,SAAAqF,CAAUC,EAAU,EAAGC,EAAS,IAC5B,MAAMtF,EAAMmB,KAAKqB,QACX+C,EAAO,IAAMD,EACbE,EAAM,CAACrE,MACb,IAAKnB,EAAIpD,GAAKoD,EAAIpD,GAAM2I,EAAOF,GAAY,GAAK,KAAO,MAAOA,GAC1DrF,EAAIpD,GAAKoD,EAAIpD,EAAI2I,GAAQ,IACzBC,EAAIC,KAAK,IAAI1E,EAAUf,IAE3B,OAAOwF,CACV,CAID,UAAAE,GACI,MAAM1F,EAAMmB,KAAKqB,QAEjB,OADAxC,EAAIpD,GAAKoD,EAAIpD,EAAI,KAAO,IACjB,IAAImE,EAAUf,EACxB,CACD,aAAA2F,CAAcN,EAAU,GACpB,MAAMnF,EAAMiB,KAAKsB,SACX7F,EAAEA,GAAMsD,GACRrD,EAAEA,GAAMqD,EACd,IAAI7C,EAAEA,GAAM6C,EACZ,MAAM0F,EAAM,GACNC,EAAe,EAAIR,EACzB,KAAOA,KACHO,EAAIH,KAAK,IAAI1E,EAAU,CAAEnE,IAAGC,IAAGQ,OAC/BA,GAAKA,EAAIwI,GAAgB,EAE7B,OAAOD,CACV,CACD,eAAAE,GACI,MAAM9F,EAAMmB,KAAKqB,SACX5F,EAAEA,GAAMoD,EACd,MAAO,CACHmB,KACA,IAAIJ,EAAU,CAAEnE,GAAIA,EAAI,IAAM,IAAKC,EAAGmD,EAAInD,EAAGC,EAAGkD,EAAIlD,IACpD,IAAIiE,EAAU,CAAEnE,GAAIA,EAAI,KAAO,IAAKC,EAAGmD,EAAInD,EAAGC,EAAGkD,EAAIlD,IAE5D,CAID,YAAAiJ,CAAaC,GACT,MAAMC,EAAK9E,KAAKQ,QACVuE,EAAK,IAAInF,EAAUiF,GAAYrE,QAC/BW,EAAQ2D,EAAGhK,EAAIiK,EAAGjK,GAAK,EAAIgK,EAAGhK,GACpC,OAAO,IAAI8E,EAAU,CACjBtE,GAAIwJ,EAAGxJ,EAAIwJ,EAAGhK,EAAIiK,EAAGzJ,EAAIyJ,EAAGjK,GAAK,EAAIgK,EAAGhK,IAAMqG,EAC9C5F,GAAIuJ,EAAGvJ,EAAIuJ,EAAGhK,EAAIiK,EAAGxJ,EAAIwJ,EAAGjK,GAAK,EAAIgK,EAAGhK,IAAMqG,EAC9C3F,GAAIsJ,EAAGtJ,EAAIsJ,EAAGhK,EAAIiK,EAAGvJ,EAAIuJ,EAAGjK,GAAK,EAAIgK,EAAGhK,IAAMqG,EAC9CrG,EAAGqG,GAEV,CAID,KAAA6D,GACI,OAAOhF,KAAKiF,OAAO,EACtB,CAID,MAAAC,GACI,OAAOlF,KAAKiF,OAAO,EACtB,CAKD,MAAAA,CAAOlL,GACH,MAAM8E,EAAMmB,KAAKqB,SACX5F,EAAEA,GAAMoD,EACRsG,EAAS,CAACnF,MACVoF,EAAY,IAAMrL,EACxB,IAAK,IAAI4D,EAAI,EAAGA,EAAI5D,EAAG4D,IACnBwH,EAAOb,KAAK,IAAI1E,EAAU,CAAEnE,GAAIA,EAAIkC,EAAIyH,GAAa,IAAK1J,EAAGmD,EAAInD,EAAGC,EAAGkD,EAAIlD,KAE/E,OAAOwJ,CACV,CAID,MAAAE,CAAOjI,GACH,MAAMkI,EAAgB,IAAI1F,EAAUxC,GAKpC,MAAoB,SAAhB4C,KAAKzC,QAA8C,SAAzB+H,EAAc/H,OACjCyC,KAAKwC,iBAAmB8C,EAAc9C,eAE1CxC,KAAKiC,gBAAkBqD,EAAcrD,aAC/C,ECpXL,SAASsD,EAAazB,GAEdA,GAAO,KAAOA,GAAO,MACrBA,GAAO,KAEX,IAAK,MAAM0B,KAASC,EAAQ,CACxB,MAAMrI,EAAQsI,EAAYF,GAC1B,GAAIpI,EAAMuI,UAAY7B,GAAO1G,EAAMuI,SAAS,IAAM7B,GAAO1G,EAAMuI,SAAS,GACpE,OAAOvI,CAEd,CACD,MAAMwI,MAAM,kBAChB,CACA,SAASC,EAAaC,EAAOC,GACzB,QAAaC,IAATD,EACA,OAAOzL,KAAKsD,MAAMkI,EAAM,GAAKxL,KAAK2L,UAAYH,EAAM,GAAK,EAAIA,EAAM,KAGvE,MAAM9L,EAAM8L,EAAM,IAAM,EAClBvL,EAAMuL,EAAM,IAAM,EAElBxD,GADNyD,GAAe,KAAPA,EAAc,OAAS,QACZ,OACnB,OAAOzL,KAAKsD,MAAMrD,EAAM+H,GAAOtI,EAAMO,GACzC,CACA,SAASmL,EAAYF,GACjB,MAAMU,EAAOV,EAAMW,YAAY,GAAG,GAC5BC,EAAOZ,EAAMW,YAAYX,EAAMW,YAAY/K,OAAS,GAAG,GACvDiL,EAAOb,EAAMW,YAAYX,EAAMW,YAAY/K,OAAS,GAAG,GACvDkL,EAAOd,EAAMW,YAAY,GAAG,GAClC,MAAO,CACHI,KAAMf,EAAMe,KACZZ,SAAUH,EAAMG,SAChBQ,YAAaX,EAAMW,YACnBK,gBAAiB,CAACN,EAAME,GACxBK,gBAAiB,CAACJ,EAAMC,GAEhC,CAIO,MAAMb,EAAS,CAClB,CACIc,KAAM,aACNZ,SAAU,KACVQ,YAAa,CACT,CAAC,EAAG,GACJ,CAAC,IAAK,KAGd,CACII,KAAM,MACNZ,SAAU,EAAE,GAAI,IAChBQ,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,SACNZ,SAAU,CAAC,GAAI,IACfQ,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,SACNZ,SAAU,CAAC,GAAI,IACfQ,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,QACNZ,SAAU,CAAC,GAAI,KACfQ,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,OACNZ,SAAU,CAAC,IAAK,KAChBQ,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,SACNZ,SAAU,CAAC,IAAK,KAChBQ,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,OACNZ,SAAU,CAAC,IAAK,KAChBQ,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,OCtQX,SAASO,EAAYC,EAAQC,GAChC,MAAMC,EAAK,IAAIjH,EAAU+G,GACnBG,EAAK,IAAIlH,EAAUgH,GACzB,OAAStM,KAAKN,IAAI6M,EAAGpG,eAAgBqG,EAAGrG,gBAAkB,MACrDnG,KAAKC,IAAIsM,EAAGpG,eAAgBqG,EAAGrG,gBAAkB,IAC1D,CCPK,MAACsG,EAAe,CACjBnH,YACA8G,cACAM,aDiDG,SAASA,EAAaC,EAAWC,EAAWC,EAAO,CAAEC,uBAAuB,EAAOC,MAAO,KAAMC,KAAM,UACzG,IAAIC,EAAY,KACZC,EAAY,EAChB,MAAMJ,sBAAEA,EAAqBC,MAAEA,EAAKC,KAAEA,GAASH,EAC/C,IAAK,MAAM/J,KAAS8J,EAAW,CAC3B,MAAMO,EAAQf,EAAYO,EAAW7J,GACjCqK,EAAQD,IACRA,EAAYC,EACZF,EAAY,IAAI3H,EAAUxC,GAEjC,CACD,OA1CG,SAAoBuJ,EAAQC,EAAQc,EAAQ,CAAEL,MAAO,KAAMC,KAAM,UACpE,MAAMK,EAAmBjB,EAAYC,EAAQC,GAC7C,QAASc,EAAML,OAAS,OAASK,EAAMJ,MAAQ,UAC3C,IAAK,UACL,IAAK,WACD,OAAOK,GAAoB,IAC/B,IAAK,UACD,OAAOA,GAAoB,EAC/B,IAAK,WACD,OAAOA,GAAoB,EAC/B,QACI,OAAO,EAEnB,CA6BQC,CAAWX,EAAWM,EAAW,CAAEF,QAAOC,WAAYF,EAC/CG,GAEXJ,EAAKC,uBAAwB,EACtBJ,EAAaC,EAAW,CAAC,OAAQ,QAASE,GACrD,EChEIlB,OFRG,SAASA,EAAO4B,EAAU,IAE7B,QAAsB7B,IAAlB6B,EAAQC,OACU,OAAlBD,EAAQC,MAAgB,CACxB,MAAMC,EAAcF,EAAQC,MACtBE,EAAS,GAEf,IADAH,EAAQC,WAAQ9B,EACT+B,EAAcC,EAAO5M,QAIxByM,EAAQC,MAAQ,KACZD,EAAQ9B,OACR8B,EAAQ9B,MAAQ,GAEpBiC,EAAO1D,KAAK2B,EAAO4B,IAGvB,OADAA,EAAQC,MAAQC,EACTC,CACV,CAED,MAAMvM,EAYV,SAAiBqI,EAAKiC,GAElB,IAAItB,EAAMoB,EAmEd,SAAqBoC,GACjB,MAAMC,EAAM1N,SAASyN,EAAY,IACjC,IAAKhN,OAAOF,MAAMmN,IAAQA,EAAM,KAAOA,EAAM,EACzC,MAAO,CAACA,EAAKA,GAEjB,GAA0B,iBAAfD,EAAyB,CAChC,MAAME,EAAa1C,EAAO2C,MAAKrO,GAAKA,EAAEwM,OAAS0B,IAC/C,GAAIE,EAAY,CACZ,MAAM/K,EAAQsI,EAAYyC,GAC1B,GAAI/K,EAAMuI,SACN,OAAOvI,EAAMuI,QAEpB,CACD,MAAM0C,EAAS,IAAIzI,EAAUqI,GAC7B,GAAII,EAAOjI,QAAS,CAChB,MAAM0D,EAAMuE,EAAO/G,QAAQ7F,EAC3B,MAAO,CAACqI,EAAKA,EAChB,CACJ,CACD,MAAO,CAAC,EAAG,IACf,CAxFqBwE,CAAYxE,GACIiC,GAG7BtB,EAAM,IACNA,EAAM,IAAMA,GAEhB,OAAOA,CACX,CArBc8D,CAAQV,EAAQ/D,IAAK+D,EAAQ9B,MAEjCrK,EAoBV,SAAwBoI,EAAK+D,GACzB,GAAoB,eAAhBA,EAAQ/D,IACR,OAAO,EAEX,GAA2B,WAAvB+D,EAAQW,WACR,OAAO3C,EAAa,CAAC,EAAG,KAAMgC,EAAQ9B,MAE1C,MAAMS,gBAAEA,GAAoBjB,EAAazB,GACzC,IAAIoC,EAAOM,EAAgB,GACvBJ,EAAOI,EAAgB,GAC3B,OAAQqB,EAAQW,YACZ,IAAK,SACDtC,EAAO,GACP,MACJ,IAAK,OACDA,EAAOE,EAAO,GACd,MACJ,IAAK,QACDA,EAAO,GAKf,OAAOP,EAAa,CAACK,EAAME,GAAOyB,EAAQ9B,KAC9C,CA5Cc0C,CAAehN,EAAGoM,GAEtB3L,EA2CV,SAAwBwM,EAAGC,EAAGd,GAC1B,IAAIxB,EAkBR,SAA8BqC,EAAGC,GAC7B,MAAMxC,YAAEA,GAAgBZ,EAAamD,GACrC,IAAK,IAAI/K,EAAI,EAAGA,EAAIwI,EAAY/K,OAAS,EAAGuC,IAAK,CAC7C,MAAMiL,EAAKzC,EAAYxI,GAAG,GACpBkL,EAAK1C,EAAYxI,GAAG,GACpBmL,EAAK3C,EAAYxI,EAAI,GAAG,GACxBoL,EAAK5C,EAAYxI,EAAI,GAAG,GAC9B,GAAIgL,GAAKC,GAAMD,GAAKG,EAAI,CACpB,MAAMhM,GAAKiM,EAAKF,IAAOC,EAAKF,GAE5B,OAAO9L,EAAI6L,GADDE,EAAK/L,EAAI8L,EAEtB,CACJ,CACD,OAAO,CACX,CAhCeI,CAAqBN,EAAGC,GAC/BrC,EAAO,IACX,OAAQuB,EAAQW,YACZ,IAAK,OACDlC,EAAOD,EAAO,GACd,MACJ,IAAK,QACDA,GAAQC,EAAOD,GAAQ,EACvB,MACJ,IAAK,SACDA,EAAO,EACPC,EAAO,IAKf,OAAOT,EAAa,CAACQ,EAAMC,GAAOuB,EAAQ9B,KAC9C,CA7DckD,CAAexN,EAAGC,EAAGmM,GACzBpD,EAAM,CAAEhJ,IAAGC,IAAGQ,KAKpB,YAJsB8J,IAAlB6B,EAAQ1G,QACRsD,EAAI3J,EAAI+M,EAAQ1G,OAGb,IAAIvB,EAAU6E,EACzB,EEvBIhU,QACAyY,UCPG,SAAmBC,EAAOrJ,GAC7B,MAAMsJ,EAAW,CACb9N,EAAGN,EAAoBmO,EAAM7N,GAC7BC,EAAGP,EAAoBmO,EAAM5N,GAC7BC,EAAGR,EAAoBmO,EAAM3N,IAKjC,YAHgBwK,IAAZmD,EAAMrO,IACNsO,EAAStO,EAAIG,OAAOkO,EAAMrO,IAEvB,IAAI8E,EAAUwJ,EAAUtJ,EACnC,EDFIuJ,aCIG,WACH,OAAO,IAAIzJ,EAAU,CACjBtE,EAAGhB,KAAK2L,SACR1K,EAAGjB,KAAK2L,SACRzK,EAAGlB,KAAK2L,UAEhB,EDTIqD,WEVG,SAAoBC,EAAYC,GACnC,MAAMpM,EAAQ,IAAIwC,EAAU2J,GACtBE,EAAa,IAAM9M,EAAcS,EAAM9B,EAAG8B,EAAM7B,EAAG6B,EAAM5B,EAAG4B,EAAMtC,GACxE,IAAI4O,EAAmBD,EACvB,MAAMtJ,EAAe/C,EAAM+C,aAAe,qBAAuB,GACjE,GAAIqJ,EAAa,CACb,MAAM9N,EAAI,IAAIkE,EAAU4J,GACxBE,EAAmB,IAAM/M,EAAcjB,EAAEJ,EAAGI,EAAEH,EAAGG,EAAEF,EAAGE,EAAEZ,EAC3D,CACD,MAAO,8CAA8CqF,kBAA6BsJ,iBAA0BC,IAChH,EFCIvM,aACAK,sBACAC"}