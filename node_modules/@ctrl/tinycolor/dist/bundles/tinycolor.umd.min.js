!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).tinycolor=e()}(this,(function(){"use strict";const t={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function e(t,e){(function(t){return"string"==typeof t&&-1!==t.indexOf(".")&&1===parseFloat(t)})(t)&&(t="100%");const r=function(t){return"string"==typeof t&&-1!==t.indexOf("%")}(t);return t=360===e?t:Math.min(e,Math.max(0,parseFloat(t))),r&&(t=parseInt(String(t*e),10)/100),Math.abs(t-e)<1e-6?1:t=360===e?(t<0?t%e+e:t%e)/parseFloat(String(e)):t%e/parseFloat(String(e))}function r(t){return Math.min(1,Math.max(0,t))}function n(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function a(t){return Number(t)<=1?100*Number(t)+"%":t}function s(t){return 1===t.length?"0"+t:String(t)}function o(t,r,n){t=e(t,255),r=e(r,255),n=e(n,255);const a=Math.max(t,r,n),s=Math.min(t,r,n);let o=0,i=0;const h=(a+s)/2;if(a===s)i=0,o=0;else{const e=a-s;switch(i=h>.5?e/(2-a-s):e/(a+s),a){case t:o=(r-n)/e+(r<n?6:0);break;case r:o=(n-t)/e+2;break;case n:o=(t-r)/e+4}o/=6}return{h:o,s:i,l:h}}function i(t,e,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?t+6*r*(e-t):r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function h(t,r,n){t=e(t,255),r=e(r,255),n=e(n,255);const a=Math.max(t,r,n),s=Math.min(t,r,n);let o=0;const i=a,h=a-s,u=0===a?0:h/a;if(a===s)o=0;else{switch(a){case t:o=(r-n)/h+(r<n?6:0);break;case r:o=(n-t)/h+2;break;case n:o=(t-r)/h+4}o/=6}return{h:o,s:u,v:i}}function u(t,e,r,n){const a=[s(Math.round(t).toString(16)),s(Math.round(e).toString(16)),s(Math.round(r).toString(16))];return n&&a[0].startsWith(a[0].charAt(1))&&a[1].startsWith(a[1].charAt(1))&&a[2].startsWith(a[2].charAt(1))?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0):a.join("")}function f(t,e,r,n){return[s(c(n)),s(Math.round(t).toString(16)),s(Math.round(e).toString(16)),s(Math.round(r).toString(16))].join("")}function l(t,e,r){let n=1-t/255,a=1-e/255,s=1-r/255,o=Math.min(n,a,s);return 1===o?(n=0,a=0,s=0):(n=(n-o)/(1-o)*100,a=(a-o)/(1-o)*100,s=(s-o)/(1-o)*100),o*=100,{c:Math.round(n),m:Math.round(a),y:Math.round(s),k:Math.round(o)}}function c(t){return Math.round(255*parseFloat(t)).toString(16)}function g(t){return d(t)/255}function d(t){return parseInt(t,16)}function b(t){let r={r:0,g:0,b:0},s=1,o=null,h=null,u=null,f=!1,l=!1;var c,g,d;return"string"==typeof t&&(t=M(t)),"object"==typeof t&&(k(t.r)&&k(t.g)&&k(t.b)?(c=t.r,g=t.g,d=t.b,r={r:255*e(c,255),g:255*e(g,255),b:255*e(d,255)},f=!0,l="%"===String(t.r).substr(-1)?"prgb":"rgb"):k(t.h)&&k(t.s)&&k(t.v)?(o=a(t.s),h=a(t.v),r=function(t,r,n){t=6*e(t,360),r=e(r,100),n=e(n,100);const a=Math.floor(t),s=t-a,o=n*(1-r),i=n*(1-s*r),h=n*(1-(1-s)*r),u=a%6;return{r:255*[n,i,o,o,h,n][u],g:255*[h,n,n,i,o,o][u],b:255*[o,o,h,n,n,i][u]}}(t.h,o,h),f=!0,l="hsv"):k(t.h)&&k(t.s)&&k(t.l)?(o=a(t.s),u=a(t.l),r=function(t,r,n){let a,s,o;if(t=e(t,360),r=e(r,100),n=e(n,100),0===r)s=n,o=n,a=n;else{const e=n<.5?n*(1+r):n+r-n*r,h=2*n-e;a=i(h,e,t+1/3),s=i(h,e,t),o=i(h,e,t-1/3)}return{r:255*a,g:255*s,b:255*o}}(t.h,o,u),f=!0,l="hsl"):k(t.c)&&k(t.m)&&k(t.y)&&k(t.k)&&(r=function(t,e,r,n){const a=n/100;return{r:255*(1-t/100)*(1-a),g:255*(1-e/100)*(1-a),b:255*(1-r/100)*(1-a)}}(t.c,t.m,t.y,t.k),f=!0,l="cmyk"),Object.prototype.hasOwnProperty.call(t,"a")&&(s=t.a)),s=n(s),{ok:f,format:t.format||l,r:Math.min(255,Math.max(r.r,0)),g:Math.min(255,Math.max(r.g,0)),b:Math.min(255,Math.max(r.b,0)),a:s}}const m="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)",p="[\\s|\\(]+("+m+")[,|\\s]+("+m+")[,|\\s]+("+m+")\\s*\\)?",w="[\\s|\\(]+("+m+")[,|\\s]+("+m+")[,|\\s]+("+m+")[,|\\s]+("+m+")\\s*\\)?",y={CSS_UNIT:new RegExp(m),rgb:new RegExp("rgb"+p),rgba:new RegExp("rgba"+w),hsl:new RegExp("hsl"+p),hsla:new RegExp("hsla"+w),hsv:new RegExp("hsv"+p),hsva:new RegExp("hsva"+w),cmyk:new RegExp("cmyk"+w),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function M(e){if(0===(e=e.trim().toLowerCase()).length)return!1;let r=!1;if(t[e])e=t[e],r=!0;else if("transparent"===e)return{r:0,g:0,b:0,a:0,format:"name"};let n=y.rgb.exec(e);return n?{r:n[1],g:n[2],b:n[3]}:(n=y.rgba.exec(e),n?{r:n[1],g:n[2],b:n[3],a:n[4]}:(n=y.hsl.exec(e),n?{h:n[1],s:n[2],l:n[3]}:(n=y.hsla.exec(e),n?{h:n[1],s:n[2],l:n[3],a:n[4]}:(n=y.hsv.exec(e),n?{h:n[1],s:n[2],v:n[3]}:(n=y.hsva.exec(e),n?{h:n[1],s:n[2],v:n[3],a:n[4]}:(n=y.cmyk.exec(e),n?{c:n[1],m:n[2],y:n[3],k:n[4]}:(n=y.hex8.exec(e),n?{r:d(n[1]),g:d(n[2]),b:d(n[3]),a:g(n[4]),format:r?"name":"hex8"}:(n=y.hex6.exec(e),n?{r:d(n[1]),g:d(n[2]),b:d(n[3]),format:r?"name":"hex"}:(n=y.hex4.exec(e),n?{r:d(n[1]+n[1]),g:d(n[2]+n[2]),b:d(n[3]+n[3]),a:g(n[4]+n[4]),format:r?"name":"hex8"}:(n=y.hex3.exec(e),!!n&&{r:d(n[1]+n[1]),g:d(n[2]+n[2]),b:d(n[3]+n[3]),format:r?"name":"hex"}))))))))))}function k(t){return"number"==typeof t?!Number.isNaN(t):y.CSS_UNIT.test(t)}class x{constructor(t="",e={}){if(t instanceof x)return t;"number"==typeof t&&(t=function(t){return{r:t>>16,g:(65280&t)>>8,b:255&t}}(t)),this.originalInput=t;const r=b(t);this.originalInput=t,this.r=r.r,this.g=r.g,this.b=r.b,this.a=r.a,this.roundA=Math.round(100*this.a)/100,this.format=e.format??r.format,this.gradientType=e.gradientType,this.r<1&&(this.r=Math.round(this.r)),this.g<1&&(this.g=Math.round(this.g)),this.b<1&&(this.b=Math.round(this.b)),this.isValid=r.ok}isDark(){return this.getBrightness()<128}isLight(){return!this.isDark()}getBrightness(){const t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3}getLuminance(){const t=this.toRgb();let e,r,n;const a=t.r/255,s=t.g/255,o=t.b/255;return e=a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4),r=s<=.03928?s/12.92:Math.pow((s+.055)/1.055,2.4),n=o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4),.2126*e+.7152*r+.0722*n}getAlpha(){return this.a}setAlpha(t){return this.a=n(t),this.roundA=Math.round(100*this.a)/100,this}isMonochrome(){const{s:t}=this.toHsl();return 0===t}toHsv(){const t=h(this.r,this.g,this.b);return{h:360*t.h,s:t.s,v:t.v,a:this.a}}toHsvString(){const t=h(this.r,this.g,this.b),e=Math.round(360*t.h),r=Math.round(100*t.s),n=Math.round(100*t.v);return 1===this.a?`hsv(${e}, ${r}%, ${n}%)`:`hsva(${e}, ${r}%, ${n}%, ${this.roundA})`}toHsl(){const t=o(this.r,this.g,this.b);return{h:360*t.h,s:t.s,l:t.l,a:this.a}}toHslString(){const t=o(this.r,this.g,this.b),e=Math.round(360*t.h),r=Math.round(100*t.s),n=Math.round(100*t.l);return 1===this.a?`hsl(${e}, ${r}%, ${n}%)`:`hsla(${e}, ${r}%, ${n}%, ${this.roundA})`}toHex(t=!1){return u(this.r,this.g,this.b,t)}toHexString(t=!1){return"#"+this.toHex(t)}toHex8(t=!1){return function(t,e,r,n,a){const o=[s(Math.round(t).toString(16)),s(Math.round(e).toString(16)),s(Math.round(r).toString(16)),s(c(n))];return a&&o[0].startsWith(o[0].charAt(1))&&o[1].startsWith(o[1].charAt(1))&&o[2].startsWith(o[2].charAt(1))&&o[3].startsWith(o[3].charAt(1))?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0)+o[3].charAt(0):o.join("")}(this.r,this.g,this.b,this.a,t)}toHex8String(t=!1){return"#"+this.toHex8(t)}toHexShortString(t=!1){return 1===this.a?this.toHexString(t):this.toHex8String(t)}toRgb(){return{r:Math.round(this.r),g:Math.round(this.g),b:Math.round(this.b),a:this.a}}toRgbString(){const t=Math.round(this.r),e=Math.round(this.g),r=Math.round(this.b);return 1===this.a?`rgb(${t}, ${e}, ${r})`:`rgba(${t}, ${e}, ${r}, ${this.roundA})`}toPercentageRgb(){const t=t=>`${Math.round(100*e(t,255))}%`;return{r:t(this.r),g:t(this.g),b:t(this.b),a:this.a}}toPercentageRgbString(){const t=t=>Math.round(100*e(t,255));return 1===this.a?`rgb(${t(this.r)}%, ${t(this.g)}%, ${t(this.b)}%)`:`rgba(${t(this.r)}%, ${t(this.g)}%, ${t(this.b)}%, ${this.roundA})`}toCmyk(){return{...l(this.r,this.g,this.b)}}toCmykString(){const{c:t,m:e,y:r,k:n}=l(this.r,this.g,this.b);return`cmyk(${t}, ${e}, ${r}, ${n})`}toName(){if(0===this.a)return"transparent";if(this.a<1)return!1;const e="#"+u(this.r,this.g,this.b,!1);for(const[r,n]of Object.entries(t))if(e===n)return r;return!1}toString(t){const e=Boolean(t);t=t??this.format;let r=!1;const n=this.a<1&&this.a>=0;return e||!n||!t.startsWith("hex")&&"name"!==t?("rgb"===t&&(r=this.toRgbString()),"prgb"===t&&(r=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(r=this.toHexString()),"hex3"===t&&(r=this.toHexString(!0)),"hex4"===t&&(r=this.toHex8String(!0)),"hex8"===t&&(r=this.toHex8String()),"name"===t&&(r=this.toName()),"hsl"===t&&(r=this.toHslString()),"hsv"===t&&(r=this.toHsvString()),"cmyk"===t&&(r=this.toCmykString()),r||this.toHexString()):"name"===t&&0===this.a?this.toName():this.toRgbString()}toNumber(){return(Math.round(this.r)<<16)+(Math.round(this.g)<<8)+Math.round(this.b)}clone(){return new x(this.toString())}lighten(t=10){const e=this.toHsl();return e.l+=t/100,e.l=r(e.l),new x(e)}brighten(t=10){const e=this.toRgb();return e.r=Math.max(0,Math.min(255,e.r-Math.round(-t/100*255))),e.g=Math.max(0,Math.min(255,e.g-Math.round(-t/100*255))),e.b=Math.max(0,Math.min(255,e.b-Math.round(-t/100*255))),new x(e)}darken(t=10){const e=this.toHsl();return e.l-=t/100,e.l=r(e.l),new x(e)}tint(t=10){return this.mix("white",t)}shade(t=10){return this.mix("black",t)}desaturate(t=10){const e=this.toHsl();return e.s-=t/100,e.s=r(e.s),new x(e)}saturate(t=10){const e=this.toHsl();return e.s+=t/100,e.s=r(e.s),new x(e)}greyscale(){return this.desaturate(100)}spin(t){const e=this.toHsl(),r=(e.h+t)%360;return e.h=r<0?360+r:r,new x(e)}mix(t,e=50){const r=this.toRgb(),n=new x(t).toRgb(),a=e/100,s={r:(n.r-r.r)*a+r.r,g:(n.g-r.g)*a+r.g,b:(n.b-r.b)*a+r.b,a:(n.a-r.a)*a+r.a};return new x(s)}analogous(t=6,e=30){const r=this.toHsl(),n=360/e,a=[this];for(r.h=(r.h-(n*t>>1)+720)%360;--t;)r.h=(r.h+n)%360,a.push(new x(r));return a}complement(){const t=this.toHsl();return t.h=(t.h+180)%360,new x(t)}monochromatic(t=6){const e=this.toHsv(),{h:r}=e,{s:n}=e;let{v:a}=e;const s=[],o=1/t;for(;t--;)s.push(new x({h:r,s:n,v:a})),a=(a+o)%1;return s}splitcomplement(){const t=this.toHsl(),{h:e}=t;return[this,new x({h:(e+72)%360,s:t.s,l:t.l}),new x({h:(e+216)%360,s:t.s,l:t.l})]}onBackground(t){const e=this.toRgb(),r=new x(t).toRgb(),n=e.a+r.a*(1-e.a);return new x({r:(e.r*e.a+r.r*r.a*(1-e.a))/n,g:(e.g*e.a+r.g*r.a*(1-e.a))/n,b:(e.b*e.a+r.b*r.a*(1-e.a))/n,a:n})}triad(){return this.polyad(3)}tetrad(){return this.polyad(4)}polyad(t){const e=this.toHsl(),{h:r}=e,n=[this],a=360/t;for(let s=1;s<t;s++)n.push(new x({h:(r+s*a)%360,s:e.s,l:e.l}));return n}equals(t){const e=new x(t);return"cmyk"===this.format||"cmyk"===e.format?this.toCmykString()===e.toCmykString():this.toRgbString()===e.toRgbString()}}function A(t){t>=334&&t<=360&&(t-=360);for(const e of R){const r=S(e);if(r.hueRange&&t>=r.hueRange[0]&&t<=r.hueRange[1])return r}throw Error("Color not found")}function v(t,e){if(void 0===e)return Math.floor(t[0]+Math.random()*(t[1]+1-t[0]));const r=t[1]||1,n=t[0]||0,a=(e=(9301*e+49297)%233280)/233280;return Math.floor(n+a*(r-n))}function S(t){const e=t.lowerBounds[0][0],r=t.lowerBounds[t.lowerBounds.length-1][0],n=t.lowerBounds[t.lowerBounds.length-1][1],a=t.lowerBounds[0][1];return{name:t.name,hueRange:t.hueRange,lowerBounds:t.lowerBounds,saturationRange:[e,r],brightnessRange:[n,a]}}const R=[{name:"monochrome",hueRange:null,lowerBounds:[[0,0],[100,0]]},{name:"red",hueRange:[-26,18],lowerBounds:[[20,100],[30,92],[40,89],[50,85],[60,78],[70,70],[80,60],[90,55],[100,50]]},{name:"orange",hueRange:[19,46],lowerBounds:[[20,100],[30,93],[40,88],[50,86],[60,85],[70,70],[100,70]]},{name:"yellow",hueRange:[47,62],lowerBounds:[[25,100],[40,94],[50,89],[60,86],[70,84],[80,82],[90,80],[100,75]]},{name:"green",hueRange:[63,178],lowerBounds:[[30,100],[40,90],[50,85],[60,81],[70,74],[80,64],[90,50],[100,40]]},{name:"blue",hueRange:[179,257],lowerBounds:[[20,100],[30,86],[40,80],[50,74],[60,60],[70,52],[80,44],[90,39],[100,35]]},{name:"purple",hueRange:[258,282],lowerBounds:[[20,100],[30,87],[40,79],[50,70],[60,65],[70,59],[80,52],[90,45],[100,42]]},{name:"pink",hueRange:[283,334],lowerBounds:[[20,100],[30,90],[40,86],[60,84],[80,80],[90,75],[100,73]]}];function $(t,e){const r=new x(t),n=new x(e);return(Math.max(r.getLuminance(),n.getLuminance())+.05)/(Math.min(r.getLuminance(),n.getLuminance())+.05)}const H={TinyColor:x,readability:$,mostReadable:function t(e,r,n={includeFallbackColors:!1,level:"AA",size:"small"}){let a=null,s=0;const{includeFallbackColors:o,level:i,size:h}=n;for(const t of r){const r=$(e,t);r>s&&(s=r,a=new x(t))}return function(t,e,r={level:"AA",size:"small"}){const n=$(t,e);switch((r.level??"AA")+(r.size??"small")){case"AAsmall":case"AAAlarge":return n>=4.5;case"AAlarge":return n>=3;case"AAAsmall":return n>=7;default:return!1}}(e,a,{level:i,size:h})||!o?a:(n.includeFallbackColors=!1,t(e,["#fff","#000"],n))},random:function t(e={}){if(void 0!==e.count&&null!==e.count){const r=e.count,n=[];for(e.count=void 0;r>n.length;)e.count=null,e.seed&&(e.seed+=1),n.push(t(e));return e.count=r,n}const r=function(t,e){let r=v(function(t){const e=parseInt(t,10);if(!Number.isNaN(e)&&e<360&&e>0)return[e,e];if("string"==typeof t){const e=R.find((e=>e.name===t));if(e){const t=S(e);if(t.hueRange)return t.hueRange}const r=new x(t);if(r.isValid){const t=r.toHsv().h;return[t,t]}}return[0,360]}(t),e);r<0&&(r=360+r);return r}(e.hue,e.seed),n=function(t,e){if("monochrome"===e.hue)return 0;if("random"===e.luminosity)return v([0,100],e.seed);const{saturationRange:r}=A(t);let n=r[0],a=r[1];switch(e.luminosity){case"bright":n=55;break;case"dark":n=a-10;break;case"light":a=55}return v([n,a],e.seed)}(r,e),a=function(t,e,r){let n=function(t,e){const{lowerBounds:r}=A(t);for(let t=0;t<r.length-1;t++){const n=r[t][0],a=r[t][1],s=r[t+1][0],o=r[t+1][1];if(e>=n&&e<=s){const t=(o-a)/(s-n);return t*e+(a-t*n)}}return 0}(t,e),a=100;switch(r.luminosity){case"dark":a=n+20;break;case"light":n=(a+n)/2;break;case"random":n=0,a=100}return v([n,a],r.seed)}(r,n,e),s={h:r,s:n,v:a};return void 0!==e.alpha&&(s.a=e.alpha),new x(s)},names:t,fromRatio:function(t,e){const r={r:a(t.r),g:a(t.g),b:a(t.b)};return void 0!==t.a&&(r.a=Number(t.a)),new x(r,e)},legacyRandom:function(){return new x({r:Math.random(),g:Math.random(),b:Math.random()})},toMsFilter:function(t,e){const r=new x(t),n="#"+f(r.r,r.g,r.b,r.a);let a=n;const s=r.gradientType?"GradientType = 1, ":"";if(e){const t=new x(e);a="#"+f(t.r,t.g,t.b,t.a)}return`progid:DXImageTransform.Microsoft.gradient(${s}startColorstr=${n},endColorstr=${a})`},inputToRGB:b,stringInputToObject:M,isValidCSSUnit:k};return H}));
//# sourceMappingURL=tinycolor.umd.min.js.map
