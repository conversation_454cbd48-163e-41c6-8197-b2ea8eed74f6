[{"name": "0-circle-fill", "title": "0 circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "0-circle", "title": "0 circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "0-square-fill", "title": "0 square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "0-square", "title": "0 square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "1-circle-fill", "title": "1 circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "1-circle", "title": "1 circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "1-square-fill", "title": "1 square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "1-square", "title": "1 square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "123", "title": 123, "categories": ["Typography"], "tags": ["numbers"]}, {"name": "2-circle-fill", "title": "2 circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "2-circle", "title": "2 circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "2-square-fill", "title": "2 square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "2-square", "title": "2 square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "3-circle-fill", "title": "3 circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "3-circle", "title": "3 circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "3-square-fill", "title": "3 square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "3-square", "title": "3 square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "4-circle-fill", "title": "4 circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "4-circle", "title": "4 circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "4-square-fill", "title": "4 square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "4-square", "title": "4 square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "5-circle-fill", "title": "5 circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "5-circle", "title": "5 circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "5-square-fill", "title": "5 square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "5-square", "title": "5 square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "6-circle-fill", "title": "6 circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "6-circle", "title": "6 circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "6-square-fill", "title": "6 square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "6-square", "title": "6 square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "7-circle-fill", "title": "7 circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "7-circle", "title": "7 circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "7-square-fill", "title": "7 square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "7-square", "title": "7 square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "8-circle-fill", "title": "8 circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "8-circle", "title": "8 circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "8-square-fill", "title": "8 square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "8-square", "title": "8 square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "9-circle-fill", "title": "9 circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "9-circle", "title": "9 circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "9-square-fill", "title": "9 square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "9-square", "title": "9 square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["number", "numeral"]}, {"name": "activity", "title": "Activity", "categories": ["Data"], "tags": ["pulse", "heartbeat", "rhythm"]}, {"name": "airplane-engines-fill", "title": "Airplane engines fill", "categories": ["Transportation"], "tags": ["flight", "flying", "plane", "air", "airport", "aircraft"]}, {"name": "airplane-engines", "title": "Airplane engines", "categories": ["Transportation"], "tags": ["flight", "flying", "plane", "air", "airport", "aircraft"]}, {"name": "airplane-fill", "title": "Airplane fill", "categories": ["Transportation"], "tags": ["flight", "flying", "plane", "air", "airport", "aircraft"]}, {"name": "airplane", "title": "Airplane", "categories": ["Transportation"], "tags": ["flight", "flying", "plane", "air", "airport", "aircraft"]}, {"name": "alarm-fill", "title": "Alarm Fill", "categories": ["Devices"], "tags": ["alarm", "clock", "time"]}, {"name": "alarm", "title": "Alarm", "categories": ["Devices"], "tags": ["alarm", "clock", "time"]}, {"name": "alexa", "title": "Alexa", "categories": ["Brand"], "tags": ["social", "assistant"]}, {"name": "align-bottom", "title": "Align bottom", "categories": ["Graphics"], "tags": ["space", "align", "distribute"]}, {"name": "align-center", "title": "Align center", "categories": ["Graphics"], "tags": ["space", "align", "distribute"]}, {"name": "align-end", "title": "Align end", "categories": ["Graphics"], "tags": ["space", "align", "distribute"]}, {"name": "align-middle", "title": "Align middle", "categories": ["Graphics"], "tags": ["space", "align", "distribute"]}, {"name": "align-start", "title": "Align start", "categories": ["Graphics"], "tags": ["space", "align", "distribute"]}, {"name": "align-top", "title": "Align top", "categories": ["Graphics"], "tags": ["space", "align", "distribute"]}, {"name": "alipay", "title": "Alipay", "categories": ["Brand"], "tags": ["payments"]}, {"name": "alphabet-uppercase", "title": "Alphabet uppercase", "categories": ["Typography"], "tags": ["letters", "abc"]}, {"name": "alphabet", "title": "Alphabet", "categories": ["Typography"], "tags": ["letters", "abc"]}, {"name": "alt", "title": "Alt", "categories": ["UI and keyboard"], "tags": ["key", "alt", "option"]}, {"name": "amazon", "title": "Amazon", "categories": ["Brand"], "tags": ["aws"]}, {"name": "amd", "title": "Amd", "categories": ["Brand"], "tags": ["radeon"]}, {"name": "android", "title": "Android", "categories": ["Brand"], "tags": ["google", "droid"]}, {"name": "android2", "title": "Android2", "categories": ["Brand"], "tags": ["google", "droid"]}, {"name": "app-indicator", "title": "App indicator", "categories": ["Apps"], "tags": ["app", "application", "ios", "android", "notification", "square"]}, {"name": "app", "title": "App", "categories": ["Apps"], "tags": ["app", "application", "ios", "android", "square"]}, {"name": "apple", "title": "Apple", "categories": ["Brand"], "tags": ["aapl", "mac", "macintosh"]}, {"name": "archive-fill", "title": "Archive fill", "categories": ["Files and folders"], "tags": ["box", "delete"]}, {"name": "archive", "title": "Archive", "categories": ["Files and folders"], "tags": ["box", "delete"]}, {"name": "arrow-90deg-down", "title": "Arrow 90deg down", "categories": ["Arrows"], "tags": ["arrow", "right-angle"]}, {"name": "arrow-90deg-left", "title": "Arrow 90deg left", "categories": ["Arrows"], "tags": ["arrow", "right-angle"]}, {"name": "arrow-90deg-right", "title": "Arrow 90deg right", "categories": ["Arrows"], "tags": ["arrow", "right-angle"]}, {"name": "arrow-90deg-up", "title": "Arrow 90deg up", "categories": ["Arrows"], "tags": ["arrow", "right-angle"]}, {"name": "arrow-bar-down", "title": "Arrow bar down", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-bar-left", "title": "Arrow bar left", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-bar-right", "title": "Arrow bar right", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-bar-up", "title": "Arrow bar up", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-clockwise", "title": "Arrow clockwise", "categories": ["Arrows"], "tags": ["arrow", "right", "spin", "turn", "around", "round", "rotate", "refresh", "reload", "redo"]}, {"name": "arrow-counterclockwise", "title": "Arrow counterclockwise", "categories": ["Arrows"], "tags": ["arrow", "left", "spin", "turn", "around", "round", "rotate", "undo"]}, {"name": "arrow-down-circle-fill", "title": "Arrow down circle fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-down-circle", "title": "Arrow down circle", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-down-left-circle-fill", "title": "Arrow down left circle fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-down-left-circle", "title": "Arrow down left circle", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-down-left-square-fill", "title": "Arrow down left square fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-down-left-square", "title": "Arrow down left square", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-down-left", "title": "Arrow down-left", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-down-right-circle-fill", "title": "Arrow down right circle fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-down-right-circle", "title": "Arrow down right circle", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-down-right-square-fill", "title": "Arrow down right square fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-down-right-square", "title": "Arrow down right square", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-down-right", "title": "Arrow down-right", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-down-short", "title": "Arrow down-short", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-down-square-fill", "title": "Arrow down square fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-down-square", "title": "Arrow down square", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-down-up", "title": "Arrow down-up", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-down", "title": "Arrow down", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-left-circle-fill", "title": "Arrow left circle fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-left-circle", "title": "Arrow left circle", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-left-right", "title": "Arrow left-right", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-left-short", "title": "Arrow left-short", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-left-square-fill", "title": "Arrow left square fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-left-square", "title": "Arrow left square", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-left", "title": "<PERSON> left", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-repeat", "title": "Arrow repeat", "categories": ["Arrows"], "tags": ["arrow", "spin", "turn", "around", "round", "rotate", "sync"]}, {"name": "arrow-return-left", "title": "Arrow return left", "categories": ["Arrows"], "tags": ["arrow", "return"]}, {"name": "arrow-return-right", "title": "Arrow return right", "categories": ["Arrows"], "tags": ["arrow", "return"]}, {"name": "arrow-right-circle-fill", "title": "Arrow right circle fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-right-circle", "title": "Arrow right circle", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-right-short", "title": "Arrow right-short", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-right-square-fill", "title": "Arrow right square fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-right-square", "title": "Arrow right square", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-right", "title": "Arrow right", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-through-heart-fill", "title": "Arrow through heart fill", "categories": ["Arrows", "Love"], "tags": ["cupid", "love", "valentine"]}, {"name": "arrow-through-heart", "title": "Arrow through heart", "categories": ["Arrows", "Love"], "tags": ["cupid", "love", "valentine"]}, {"name": "arrow-up-circle-fill", "title": "Arrow up circle fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-up-circle", "title": "Arrow up circle", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-up-left-circle-fill", "title": "Arrow up left circle fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-up-left-circle", "title": "Arrow up left circle", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-up-left-square-fill", "title": "Arrow up left square fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-up-left-square", "title": "Arrow up left square", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-up-left", "title": "<PERSON> up-left", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-up-right-circle-fill", "title": "Arrow up right circle fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-up-right-circle", "title": "Arrow up right circle", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "circle"]}, {"name": "arrow-up-right-square-fill", "title": "Arrow up right square fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-up-right-square", "title": "Arrow up right square", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-up-right", "title": "<PERSON> up-right", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-up-short", "title": "Arrow up-short", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrow-up-square-fill", "title": "Arrow up square fill", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-up-square", "title": "Arrow up square", "categories": ["<PERSON><PERSON><PERSON>s"], "tags": ["arrow", "square"]}, {"name": "arrow-up", "title": "Arrow up", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrows-angle-contract", "title": "Arrows angle contract", "categories": ["Arrows"], "tags": ["arrow", "resize"]}, {"name": "arrows-angle-expand", "title": "Arrows angle expand", "categories": ["Arrows"], "tags": ["arrow", "resize"]}, {"name": "arrows-collapse-vertical", "title": "Arrows collapse vertical", "categories": ["Arrows"], "tags": ["arrow", "resize"]}, {"name": "arrows-collapse", "title": "Arrows collapse", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrows-expand-vertical", "title": "Arrows expand vertical", "categories": ["Arrows"], "tags": ["arrow", "resize"]}, {"name": "arrows-expand", "title": "Arrows expand", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrows-fullscreen", "title": "Arrows fullscreen", "categories": ["Arrows"], "tags": ["arrow"]}, {"name": "arrows-move", "title": "Arrows move", "categories": ["Arrows"], "tags": ["arrow", "cursor", "move"]}, {"name": "arrows-vertical", "title": "Arrows vertical", "categories": ["Arrows"], "tags": ["arrow", "resize"]}, {"name": "arrows", "title": "Arrows", "categories": ["Arrows"], "tags": ["arrow", "resize"]}, {"name": "aspect-ratio-fill", "title": "Aspect ratio fill", "categories": ["Media"], "tags": ["size", "resize", "crop", "dimensions"]}, {"name": "aspect-ratio", "title": "Aspect ratio", "categories": ["Media"], "tags": ["size", "resize", "crop", "dimensions"]}, {"name": "asterisk", "title": "Asterisk", "categories": ["Typography"], "tags": ["asterisks", "star"]}, {"name": "at", "title": "At", "categories": ["Communications"], "tags": ["mention", "sign"]}, {"name": "award-fill", "title": "Award fill", "categories": ["Real world"], "tags": ["prize", "rosette"]}, {"name": "award", "title": "Award", "categories": ["Real world"], "tags": ["prize", "rosette"]}, {"name": "back", "title": "Back", "categories": ["Graphics"], "tags": ["backward", "layer"]}, {"name": "backpack-fill", "title": "Backpack fill", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "backpack", "title": "Backpack", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "backpack2-fill", "title": "Backpack2 fill", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "backpack2", "title": "Backpack2", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "backpack3-fill", "title": "Backpack3 fill", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "backpack3", "title": "Backpack3", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "backpack4-fill", "title": "Backpack4 fill", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "backpack4", "title": "Backpack4", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "backspace-fill", "title": "Backspace fill", "categories": ["UI and keyboard"], "tags": ["key"]}, {"name": "backspace-reverse-fill", "title": "Backspace reverse fill", "categories": ["UI and keyboard"], "tags": ["key"]}, {"name": "backspace-reverse", "title": "Backspace reverse", "categories": ["UI and keyboard"], "tags": ["key"]}, {"name": "backspace", "title": "Backspace", "categories": ["UI and keyboard"], "tags": ["key"]}, {"name": "badge-3d-fill", "title": "Badge 3d fill", "categories": ["Badges"], "tags": ["3d", "display", "dimension"]}, {"name": "badge-3d", "title": "Badge 3d", "categories": ["Badges"], "tags": ["3d", "display", "dimension"]}, {"name": "badge-4k-fill", "title": "Badge 4k fill", "categories": ["Badges"], "tags": ["4k", "display", "resolution", "retina"]}, {"name": "badge-4k", "title": "Badge 4k", "categories": ["Badges"], "tags": ["4k", "display", "resolution", "retina"]}, {"name": "badge-8k-fill", "title": "Badge 8k fill", "categories": ["Badges"], "tags": ["4k", "display", "resolution", "retina"]}, {"name": "badge-8k", "title": "Badge 8k", "categories": ["Badges"], "tags": ["4k", "display", "resolution", "retina"]}, {"name": "badge-ad-fill", "title": "Badge ad fill", "categories": ["Badges"], "tags": ["advertisement"]}, {"name": "badge-ad", "title": "Badge ad", "categories": ["Badges"], "tags": ["advertisement"]}, {"name": "badge-ar-fill", "title": "Badge ar fill", "categories": ["Badges"], "tags": ["augmented", "reality", "ar"]}, {"name": "badge-ar", "title": "Badge ar", "categories": ["Badges"], "tags": ["augmented", "reality", "ar"]}, {"name": "badge-cc-fill", "title": "Badge cc fill", "categories": ["Badges"], "tags": ["closed captioning"]}, {"name": "badge-cc", "title": "Badge cc", "categories": ["Badges"], "tags": ["closed captioning"]}, {"name": "badge-hd-fill", "title": "Badge hd fill", "categories": ["Badges"], "tags": ["display", "resolution", "high definition"]}, {"name": "badge-hd", "title": "Badge hd", "categories": ["Badges"], "tags": ["display", "resolution", "high definition"]}, {"name": "badge-sd-fill", "title": "Badge SD fill", "categories": ["Badges"], "tags": ["display", "resolution", "standard definition"]}, {"name": "badge-sd", "title": "Badge Sd", "categories": ["Badges"], "tags": ["display", "resolution", "standard definition"]}, {"name": "badge-tm-fill", "title": "Badge tm fill", "categories": ["Badges"], "tags": ["trademark"]}, {"name": "badge-tm", "title": "Badge tm", "categories": ["Badges"], "tags": ["trademark"]}, {"name": "badge-vo-fill", "title": "Badge vo fill", "categories": ["Badges"], "tags": ["voiceover", "accessibility"]}, {"name": "badge-vo", "title": "Badge vo", "categories": ["Badges"], "tags": ["voiceover", "accessibility"]}, {"name": "badge-vr-fill", "title": "Badge vr fill", "categories": ["Badges"], "tags": ["virtual", "reality", "vr"]}, {"name": "badge-vr", "title": "Badge vr", "categories": ["Badges"], "tags": ["virtual", "reality", "vr"]}, {"name": "badge-wc-fill", "title": "Badge wc fill", "categories": ["Badges"], "tags": ["wash closet", "wc"]}, {"name": "badge-wc", "title": "Badge wc", "categories": ["Badges"], "tags": ["wash closet", "wc"]}, {"name": "bag-check-fill", "title": "Bag check fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "bag-check", "title": "Bag check", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "bag-dash-fill", "title": "Bag dash fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "bag-dash", "title": "Bag dash", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "bag-fill", "title": "Bag fill", "categories": ["Commerce"], "tags": ["shopping", "cart", "purchase", "buy"]}, {"name": "bag-heart-fill", "title": "Bag heart fill", "categories": ["Commerce"], "tags": ["shopping", "cart", "purchase", "buy", "valentine", "love"]}, {"name": "bag-heart", "title": "Bag heart", "categories": ["Commerce"], "tags": ["shopping", "cart", "purchase", "buy", "valentine", "love"]}, {"name": "bag-plus-fill", "title": "Bag plus fill", "categories": ["Commerce"], "tags": ["shopping", "add", "cart", "basket", "bag"]}, {"name": "bag-plus", "title": "Bag plus", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "bag-x-fill", "title": "Bag x fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "bag-x", "title": "Bag x", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "bag", "title": "Bag", "categories": ["Commerce"], "tags": ["shopping", "cart", "purchase", "buy"]}, {"name": "balloon-fill", "title": "Balloon fill", "categories": ["Real World"], "tags": ["birthday"]}, {"name": "balloon-heart-fill", "title": "Balloon heart fill", "categories": ["Real World", "Love"], "tags": ["birthday", "valentine", "love"]}, {"name": "balloon-heart", "title": "Balloon heart", "categories": ["Real World", "Love"], "tags": ["birthday", "valentine", "love"]}, {"name": "balloon", "title": "Balloon", "categories": ["Real World"], "tags": ["birthday"]}, {"name": "ban-fill", "title": "Ban fill", "categories": ["Real World"], "tags": ["no", "not allowed"]}, {"name": "ban", "title": "Ban", "categories": ["Real World"], "tags": ["no", "not allowed", "block"]}, {"name": "bandaid-fill", "title": "Bandaid fill", "categories": ["Real World"], "tags": ["bandage", "health"]}, {"name": "bandaid", "title": "Bandaid", "categories": ["Real World"], "tags": ["bandage", "health"]}, {"name": "bank", "title": "Bank", "categories": ["Commerce"], "tags": ["money", "finance", "banking", "market", "temple"]}, {"name": "bank2", "title": "Bank2", "categories": ["Commerce"], "tags": ["money", "finance", "banking", "market", "temple"]}, {"name": "bar-chart-fill", "title": "Bar chart fill", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "bar-chart-line-fill", "title": "Bar chart line fill", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "bar-chart-line", "title": "Bar chart line", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "bar-chart-steps", "title": "Bar chart steps", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "bar-chart", "title": "Bar chart", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "basket-fill", "title": "Basket fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "basket", "title": "Basket", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "basket2-fill", "title": "Basket2 fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "basket2", "title": "Basket2", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "basket3-fill", "title": "Basket3 fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "basket3", "title": "Basket3", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "battery-charging", "title": "Battery charging", "categories": ["Devices"], "tags": ["power", "charge"]}, {"name": "battery-full", "title": "Battery full", "categories": ["Devices"], "tags": ["power", "charge"]}, {"name": "battery-half", "title": "Battery half", "categories": ["Devices"], "tags": ["power", "charge"]}, {"name": "battery", "title": "Battery", "categories": ["Devices"], "tags": ["power", "charge"]}, {"name": "behance", "title": "<PERSON><PERSON><PERSON>", "categories": ["Brand"], "tags": ["social"]}, {"name": "bell-fill", "title": "Bell fill", "categories": ["Communications"], "tags": ["notification", "clock"]}, {"name": "bell-slash-fill", "title": "Bell slash fill", "categories": ["Communications"], "tags": ["notification", "silenced", "clock"]}, {"name": "bell-slash", "title": "Bell slash", "categories": ["Communications"], "tags": ["notification", "silenced", "clock"]}, {"name": "bell", "title": "Bell", "categories": ["Communications"], "tags": ["notification", "clock"]}, {"name": "bezier", "title": "<PERSON><PERSON>", "categories": ["Graphics"], "tags": ["graphics", "vector", "pen"]}, {"name": "bezier2", "title": "Bezier2", "categories": ["Graphics"], "tags": ["graphics", "vector", "pen"]}, {"name": "bicycle", "title": "Bicycle", "categories": ["Real world"], "tags": ["bike", "riding", "bicycling"]}, {"name": "bing", "title": "<PERSON>", "categories": ["Brand"], "tags": ["search", "microsoft"]}, {"name": "binoculars-fill", "title": "Binoculars fill", "categories": ["Real world"], "tags": ["distance", "view", "search"]}, {"name": "binoculars", "title": "Binoculars", "categories": ["Real world"], "tags": ["distance", "view", "search"]}, {"name": "blockquote-left", "title": "<PERSON><PERSON><PERSON><PERSON> left", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "blockquote-right", "title": "Blockquote right", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "bluetooth", "title": "Bluetooth", "categories": ["Brand"], "tags": ["wireless"]}, {"name": "body-text", "title": "Body text", "categories": ["Typography"], "tags": ["content"]}, {"name": "book-fill", "title": "Book fill", "categories": ["Real world"], "tags": ["novel", "read", "magazine"]}, {"name": "book-half", "title": "Book half", "categories": ["Real world"], "tags": ["novel", "read", "magazine"]}, {"name": "book", "title": "Book", "categories": ["Real world"], "tags": ["novel", "read", "magazine"]}, {"name": "bookmark-check-fill", "title": "Bookmark check fill", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-check", "title": "Bookmark check", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-dash-fill", "title": "Bookmark dash fill", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-dash", "title": "Bookmark dash", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-fill", "title": "Bookmark fill", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-heart-fill", "title": "Bookmark heart fill", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-heart", "title": "Bookmark heart", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-plus-fill", "title": "Bookmark plus fill", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-plus", "title": "Bookmark plus", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-star-fill", "title": "Bookmark star fill", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-star", "title": "Bookmark star", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-x-fill", "title": "Bookmark x fill", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark-x", "title": "Bookmark x", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmark", "title": "Bookmark", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmarks-fill", "title": "Bookmarks fill", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookmarks", "title": "Bookmarks", "categories": ["Miscellaneous"], "tags": ["reading", "book", "label", "tag", "category"]}, {"name": "bookshelf", "title": "Bookshelf", "categories": ["Real world"], "tags": ["shelf"]}, {"name": "boombox-fill", "title": "Boombox fill", "categories": ["Real World"], "tags": ["music"]}, {"name": "boombox", "title": "Boombox", "categories": ["Real World"], "tags": ["music"]}, {"name": "bootstrap-fill", "title": "Bootstrap fill", "categories": ["Bootstrap"], "tags": ["bootstrap"]}, {"name": "bootstrap-reboot", "title": "Bootstrap Reboot", "categories": ["Bootstrap"], "tags": ["bootstrap"]}, {"name": "bootstrap", "title": "Bootstrap", "categories": ["Bootstrap"], "tags": ["bootstrap"]}, {"name": "border-all", "title": "Border all", "categories": ["UI and keyboard"], "tags": ["borders"]}, {"name": "border-bottom", "title": "Border bottom", "categories": ["UI and keyboard"], "tags": ["borders"]}, {"name": "border-center", "title": "Border center", "categories": ["UI and keyboard"], "tags": ["borders"]}, {"name": "border-inner", "title": "Border inner", "categories": ["UI and keyboard"], "tags": ["borders"]}, {"name": "border-left", "title": "Border left", "categories": ["UI and keyboard"], "tags": ["borders"]}, {"name": "border-middle", "title": "Border middle", "categories": ["UI and keyboard"], "tags": ["borders"]}, {"name": "border-outer", "title": "Border outer", "categories": ["UI and keyboard"], "tags": ["borders"]}, {"name": "border-right", "title": "Border right", "categories": ["UI and keyboard"], "tags": ["borders"]}, {"name": "border-style", "title": "Border style", "categories": ["Typography"], "tags": ["borders", "wysiwyg"]}, {"name": "border-top", "title": "Border top", "categories": ["UI and keyboard"], "tags": ["borders"]}, {"name": "border-width", "title": "Border width", "categories": ["Typography"], "tags": ["borders", "wysiwyg"]}, {"name": "border", "title": "Border", "categories": ["UI and keyboard"], "tags": ["borders"]}, {"name": "bounding-box-circles", "title": "Bounding box circles", "categories": ["Graphics"], "tags": ["text", "shape", "resize", "dimensions"]}, {"name": "bounding-box", "title": "Bounding box", "categories": ["Graphics"], "tags": ["text", "shape", "resize", "dimensions"]}, {"name": "box-arrow-down-left", "title": "Box arrow bottom-left", "categories": ["Box arrows"], "tags": ["arrow"]}, {"name": "box-arrow-down-right", "title": "Box arrow bottom-right", "categories": ["Box arrows"], "tags": ["arrow"]}, {"name": "box-arrow-down", "title": "Box arrow down", "categories": ["Box arrows"], "tags": ["arrow", "download", "save"]}, {"name": "box-arrow-in-down-left", "title": "Box arrow in down left", "categories": ["Box arrows"], "tags": ["arrow"]}, {"name": "box-arrow-in-down-right", "title": "Box arrow in down right", "categories": ["Box arrows"], "tags": ["arrow"]}, {"name": "box-arrow-in-down", "title": "Box arrow in down", "categories": ["Box arrows"], "tags": ["arrow", "upload"]}, {"name": "box-arrow-in-left", "title": "Box arrow in left", "categories": ["Box arrows"], "tags": ["arrow", "login", "signin", "enter"]}, {"name": "box-arrow-in-right", "title": "Box arrow in right", "categories": ["Box arrows"], "tags": ["arrow", "login", "signin", "enter"]}, {"name": "box-arrow-in-up-left", "title": "Box arrow in up left", "categories": ["Box arrows"], "tags": ["arrow"]}, {"name": "box-arrow-in-up-right", "title": "Box arrow in up right", "categories": ["Box arrows"], "tags": ["arrow"]}, {"name": "box-arrow-in-up", "title": "Box arrow in up", "categories": ["Box arrows"], "tags": ["arrow"]}, {"name": "box-arrow-left", "title": "Box arrow left", "categories": ["Box arrows"], "tags": ["arrow", "logout", "signout", "exit"]}, {"name": "box-arrow-right", "title": "Box arrow right", "categories": ["Box arrows"], "tags": ["arrow", "logout", "signout", "exit"]}, {"name": "box-arrow-up-left", "title": "Box arrow up-left", "categories": ["Box arrows"], "tags": ["arrow", "external link"]}, {"name": "box-arrow-up-right", "title": "Box arrow up-right", "categories": ["Box arrows"], "tags": ["arrow", "external link"]}, {"name": "box-arrow-up", "title": "Box arrow up", "categories": ["Box arrows"], "tags": ["arrow"]}, {"name": "box-fill", "title": "Box fill", "categories": ["Real world"], "tags": ["cardboard", "package"]}, {"name": "box-seam-fill", "title": "Box seam fill", "categories": ["Real world"], "tags": ["cardboard", "package"]}, {"name": "box-seam", "title": "Box seam", "categories": ["Real world"], "tags": ["cardboard", "package"]}, {"name": "box", "title": "Box", "categories": ["Real world"], "tags": ["cardboard", "package", "cube"]}, {"name": "box2-fill", "title": "Box2 fill", "categories": ["Real World", "Love"], "tags": ["cardboard", "package", "cube"]}, {"name": "box2-heart-fill", "title": "Box2 heart fill", "categories": ["Real World", "Love"], "tags": ["cardboard", "package", "cube", "gift", "valentine", "love"]}, {"name": "box2-heart", "title": "Box2 heart", "categories": ["Real World", "Love"], "tags": ["cardboard", "package", "cube", "gift", "valentine", "love"]}, {"name": "box2", "title": "Box2", "categories": ["Real World"], "tags": ["cardboard", "package", "cube"]}, {"name": "boxes", "title": "Boxes", "categories": ["Real World"], "tags": ["cardboard", "package", "cube"]}, {"name": "braces-asterisk", "title": "Braces asterisk", "categories": ["Typography"], "tags": ["text", "type", "code", "developer", "development", "software"]}, {"name": "braces", "title": "Braces", "categories": ["Typography"], "tags": ["text", "type", "code", "developer", "development", "software"]}, {"name": "bricks", "title": "Bricks", "categories": ["Real world"], "tags": ["wall", "firewall"]}, {"name": "briefcase-fill", "title": "Briefcase fill", "categories": ["Real world"], "tags": ["business", "bag", "baggage"]}, {"name": "briefcase", "title": "Briefcase", "categories": ["Real world"], "tags": ["business", "bag", "baggage"]}, {"name": "brightness-alt-high-fill", "title": "Brightness alt high fill", "categories": ["UI and keyboard"], "tags": ["brightness", "sun", "weather"]}, {"name": "brightness-alt-high", "title": "Brightness alt high", "categories": ["UI and keyboard"], "tags": ["brightness", "sun", "weather"]}, {"name": "brightness-alt-low-fill", "title": "Brightness alt low fill", "categories": ["UI and keyboard"], "tags": ["brightness", "sun", "weather"]}, {"name": "brightness-alt-low", "title": "Brightness alt low", "categories": ["UI and keyboard"], "tags": ["brightness", "sun", "weather"]}, {"name": "brightness-high-fill", "title": "Brightness high fill", "categories": ["UI and keyboard"], "tags": ["brightness", "sun", "weather"]}, {"name": "brightness-high", "title": "Brightness high", "categories": ["UI and keyboard"], "tags": ["brightness", "sun", "weather"]}, {"name": "brightness-low-fill", "title": "Brightness low fill", "categories": ["UI and keyboard"], "tags": ["brightness", "sun", "weather"]}, {"name": "brightness-low", "title": "Brightness low", "categories": ["UI and keyboard"], "tags": ["brightness", "sun", "weather"]}, {"name": "brilliance", "title": "Brilliance", "categories": ["Graphics"], "tags": ["photo", "editing", "image", "picture"]}, {"name": "broadcast-pin", "title": "Broadcast pin", "categories": ["Communications"], "tags": ["radio", "radio wave", "amplify", "wavelength", "podcast"]}, {"name": "broadcast", "title": "Broadcast", "categories": ["Communications"], "tags": ["radio", "radio wave", "amplify", "wavelength"]}, {"name": "browser-chrome", "title": "Browser Chrome", "categories": ["Brand"], "tags": ["google", "webkit", "blink"]}, {"name": "browser-edge", "title": "Browser Edge", "categories": ["Brand"], "tags": ["microsoft", "webkit"]}, {"name": "browser-firefox", "title": "Browser Firefox", "categories": ["Brand"], "tags": ["gecko"]}, {"name": "browser-safari", "title": "Browser Safari", "categories": ["Brand"], "tags": ["webkit", "apple"]}, {"name": "brush-fill", "title": "Brush fill", "categories": ["Tools"], "tags": ["paint", "art"]}, {"name": "brush", "title": "Brush", "categories": ["Tools"], "tags": ["paint", "art"]}, {"name": "bucket-fill", "title": "Bucket fill", "categories": ["Tools"], "tags": ["tool", "pail"]}, {"name": "bucket", "title": "Bucket", "categories": ["Tools"], "tags": ["tool", "pail"]}, {"name": "bug-fill", "title": "Bug fill", "categories": ["Real world"], "tags": ["insect", "error"]}, {"name": "bug", "title": "Bug", "categories": ["Real world"], "tags": ["insect", "error"]}, {"name": "building-add", "title": "Building add", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-check", "title": "Building check", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-dash", "title": "Building dash", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-down", "title": "Building down", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-exclamation", "title": "Building exclamation", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill-add", "title": "Building fill add", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill-check", "title": "Building fill check", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill-dash", "title": "Building fill dash", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill-down", "title": "Building fill down", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill-exclamation", "title": "Building fill exclamation", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill-gear", "title": "Building fill gear", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill-lock", "title": "Building fill lock", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill-slash", "title": "Building fill slash", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill-up", "title": "Building fill up", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill-x", "title": "Building fill x", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-fill", "title": "Building fill", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-gear", "title": "Building gear", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-lock", "title": "Building lock", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-slash", "title": "Building slash", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-up", "title": "Building up", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building-x", "title": "Building x", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "building", "title": "Building", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "buildings-fill", "title": "Buildings fill", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "buildings", "title": "Buildings", "categories": ["Buildings"], "tags": ["company", "enterprise", "organization", "office", "business"]}, {"name": "bullseye", "title": "Bullseye", "categories": ["Geo"], "tags": ["target"]}, {"name": "bus-front-fill", "title": "Bus front fill", "categories": ["Transportation"], "tags": ["public transit", "commute"]}, {"name": "bus-front", "title": "Bus front", "categories": ["Transportation"], "tags": ["public transit", "commute"]}, {"name": "c-circle-fill", "title": "C circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["copyright"]}, {"name": "c-circle", "title": "C circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["copyright"]}, {"name": "c-square-fill", "title": "C square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["copyright"]}, {"name": "c-square", "title": "C square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["copyright"]}, {"name": "cake-fill", "title": "Cake fill", "categories": ["Real World"], "tags": ["birthday", "celebrate", "dessert"]}, {"name": "cake", "title": "Cake", "categories": ["Real World"], "tags": ["birthday", "celebrate", "dessert"]}, {"name": "cake2-fill", "title": "Cake2 fill", "categories": ["Real World"], "tags": ["birthday", "celebrate", "dessert"]}, {"name": "cake2", "title": "Cake2", "categories": ["Real World"], "tags": ["birthday", "celebrate", "dessert"]}, {"name": "calculator-fill", "title": "Calculator fill", "categories": ["Devices"], "tags": ["calculator", "math"]}, {"name": "calculator", "title": "Calculator", "categories": ["Devices"], "tags": ["calculator", "math"]}, {"name": "calendar-check-fill", "title": "Calendar check fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-check", "title": "Calendar check", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-date-fill", "title": "Calendar date fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-date", "title": "Calendar date", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-day-fill", "title": "Calendar day fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-day", "title": "Calendar day", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-event-fill", "title": "Calendar event fill", "categories": ["Date and time"], "tags": ["date", "time", "event", "invite"]}, {"name": "calendar-event", "title": "Calendar event", "categories": ["Date and time"], "tags": ["date", "time", "event", "invite"]}, {"name": "calendar-fill", "title": "Calendar fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-heart-fill", "title": "Calendar heart fill", "categories": ["Date and time", "Love"], "tags": ["date", "time", "month", "valentine", "date"]}, {"name": "calendar-heart", "title": "Calendar heart", "categories": ["Date and time", "Love"], "tags": ["date", "time", "month", "valentine", "date"]}, {"name": "calendar-minus-fill", "title": "Calendar minus fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-minus", "title": "Calendar minus", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-month-fill", "title": "Calendar month fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-month", "title": "Calendar month", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-plus-fill", "title": "Calendar plus fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-plus", "title": "Calendar plus", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar-range-fill", "title": "Calendar range fill", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration"]}, {"name": "calendar-range", "title": "Calendar range", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration"]}, {"name": "calendar-week-fill", "title": "Calendar week fill", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration", "week"]}, {"name": "calendar-week", "title": "Calendar week", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration", "week"]}, {"name": "calendar-x-fill", "title": "Calendar x fill", "categories": ["Date and time"], "tags": ["date", "time", "month", "remove", "delete"]}, {"name": "calendar-x", "title": "Calendar x", "categories": ["Date and time"], "tags": ["date", "time", "month", "remove", "delete"]}, {"name": "calendar", "title": "Calendar", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-check-fill", "title": "Calendar2 check fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-check", "title": "Calendar2 check", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-date-fill", "title": "Calendar2 date fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-date", "title": "Calendar2 date", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-day-fill", "title": "Calendar2 day fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-day", "title": "Calendar2 day", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-event-fill", "title": "Calendar2 event fill", "categories": ["Date and time"], "tags": ["date", "time", "event", "invite"]}, {"name": "calendar2-event", "title": "Calendar2 event", "categories": ["Date and time"], "tags": ["date", "time", "event", "invite"]}, {"name": "calendar2-fill", "title": "Calendar2 fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-heart-fill", "title": "Calendar2 heart fill", "categories": ["Date and time", "Love"], "tags": ["date", "time", "month", "valentine", "date"]}, {"name": "calendar2-heart", "title": "Calendar2 heart", "categories": ["Date and time", "Love"], "tags": ["date", "time", "month", "valentine", "date"]}, {"name": "calendar2-minus-fill", "title": "Calendar2 minus fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-minus", "title": "Calendar2 minus", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-month-fill", "title": "Calendar2 month fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-month", "title": "Calendar2 month", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-plus-fill", "title": "Calendar2 plus fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-plus", "title": "Calendar2 plus", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar2-range-fill", "title": "Calendar2 range fill", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration"]}, {"name": "calendar2-range", "title": "Calendar2 range", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration"]}, {"name": "calendar2-week-fill", "title": "Calendar2 week fill", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration", "week"]}, {"name": "calendar2-week", "title": "Calendar2 week", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration", "week"]}, {"name": "calendar2-x-fill", "title": "Calendar2 x fill", "categories": ["Date and time"], "tags": ["date", "time", "month", "remove", "delete"]}, {"name": "calendar2-x", "title": "Calendar2 x", "categories": ["Date and time"], "tags": ["date", "time", "month", "remove", "delete"]}, {"name": "calendar2", "title": "Calendar2", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar3-event-fill", "title": "Calendar3 event fill", "categories": ["Date and time"], "tags": ["date", "time", "event", "invite"]}, {"name": "calendar3-event", "title": "Calendar3 event", "categories": ["Date and time"], "tags": ["date", "time", "event", "invite"]}, {"name": "calendar3-fill", "title": "Calendar3 fill", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar3-range-fill", "title": "Calendar3 range fill", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration"]}, {"name": "calendar3-range", "title": "Calendar3 range", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration"]}, {"name": "calendar3-week-fill", "title": "Calendar3 week fill", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration", "week"]}, {"name": "calendar3-week", "title": "Calendar3 week", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration", "week"]}, {"name": "calendar3", "title": "Calendar3", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "calendar4-event", "title": "Calendar4 event", "categories": ["Date and time"], "tags": ["date", "time", "event", "invite"]}, {"name": "calendar4-range", "title": "Calendar4 range", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration"]}, {"name": "calendar4-week", "title": "Calendar4 week", "categories": ["Date and time"], "tags": ["dates", "timeline", "duration", "week"]}, {"name": "calendar4", "title": "Calendar4", "categories": ["Date and time"], "tags": ["date", "time", "month"]}, {"name": "camera-fill", "title": "Camera fill", "categories": ["Devices"], "tags": ["photos", "photography"]}, {"name": "camera-reels-fill", "title": "Camera reels fill", "categories": ["Devices"], "tags": ["av", "video", "film"]}, {"name": "camera-reels", "title": "Camera reels", "categories": ["Devices"], "tags": ["av", "video", "film"]}, {"name": "camera-video-fill", "title": "Camera video fill", "categories": ["Devices"], "tags": ["av", "video", "film"]}, {"name": "camera-video-off-fill", "title": "Camera video off fill", "categories": ["Devices"], "tags": ["av", "video", "film"]}, {"name": "camera-video-off", "title": "Camera video off", "categories": ["Devices"], "tags": ["av", "video", "film"]}, {"name": "camera-video", "title": "Camera video", "categories": ["Devices"], "tags": ["av", "video", "film"]}, {"name": "camera", "title": "Camera", "categories": ["Devices"], "tags": ["photos", "photography"]}, {"name": "camera2", "title": "Camera", "categories": ["Devices"], "tags": ["photos", "photography"]}, {"name": "capslock-fill", "title": "Capslock fill", "categories": ["UI and keyboard"], "tags": ["key"]}, {"name": "capslock", "title": "<PERSON><PERSON><PERSON>", "categories": ["UI and keyboard"], "tags": ["key"]}, {"name": "capsule-pill", "title": "Capsule pill", "categories": ["Medical"], "tags": ["rx", "pills", "capsules", "medicine"]}, {"name": "capsule", "title": "Capsule", "categories": ["Medical"], "tags": ["rx", "pills", "capsules", "medicine"]}, {"name": "car-front-fill", "title": "Car front fill", "categories": ["Transportation"], "tags": ["automobile", "automotive", "auto", "sedan", "drive", "driving", "vehicle"]}, {"name": "car-front", "title": "Car front", "categories": ["Transportation"], "tags": ["automobile", "automotive", "auto", "sedan", "drive", "driving", "vehicle"]}, {"name": "card-checklist", "title": "Card checklist", "categories": ["Files and folders"], "tags": ["note", "card", "notecard"]}, {"name": "card-heading", "title": "Card heading", "categories": ["Files and folders"], "tags": ["note", "card", "notecard"]}, {"name": "card-image", "title": "Card image", "categories": ["Files and folders"], "tags": ["note", "card", "notecard"]}, {"name": "card-list", "title": "Card list", "categories": ["Files and folders"], "tags": ["note", "card", "notecard"]}, {"name": "card-text", "title": "Card text", "categories": ["Files and folders"], "tags": ["note", "card", "notecard", "letter"]}, {"name": "caret-down-fill", "title": "Caret down fill", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-down-square-fill", "title": "Caret down square fill", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-down-square", "title": "Caret down square", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-down", "title": "Caret down", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-left-fill", "title": "<PERSON><PERSON> left fill", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-left-square-fill", "title": "Caret left square fill", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-left-square", "title": "Caret left square", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-left", "title": "<PERSON><PERSON> left", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-right-fill", "title": "Caret right fill", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-right-square-fill", "title": "Caret right square fill", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-right-square", "title": "Caret right square", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-right", "title": "Caret right", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-up-fill", "title": "Caret up fill", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-up-square-fill", "title": "Caret up square fill", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-up-square", "title": "Caret up square", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "caret-up", "title": "Caret up", "categories": ["Carets"], "tags": ["caret", "arrow", "triangle"]}, {"name": "cart-check-fill", "title": "Cart check fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart-check", "title": "Cart check", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart-dash-fill", "title": "Cart dash fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart-dash", "title": "Cart dash", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart-fill", "title": "Cart fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart-plus-fill", "title": "Cart plus fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart-plus", "title": "Cart plus", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart-x-fill", "title": "Cart x fill", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart-x", "title": "Cart x", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart", "title": "<PERSON><PERSON>", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart2", "title": "Cart2", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart3", "title": "Cart3", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cart4", "title": "Cart4", "categories": ["Commerce"], "tags": ["shopping", "checkout", "check", "cart", "basket", "bag"]}, {"name": "cash-coin", "title": "Cash coin", "categories": ["Commerce"], "tags": ["money", "finance", "banking", "currency"]}, {"name": "cash-stack", "title": "Cash stack", "categories": ["Commerce"], "tags": ["money", "bills", "funds", "wallet", "currency"]}, {"name": "cash", "title": "Cash", "categories": ["Commerce"], "tags": ["money", "bills", "funds", "wallet", "currency"]}, {"name": "cassette-fill", "title": "Cassette fill", "categories": ["Media"], "tags": ["tape", "music", "audio"]}, {"name": "cassette", "title": "Cassette", "categories": ["Media"], "tags": ["tape", "music", "audio"]}, {"name": "cast", "title": "Cast", "categories": ["Media"], "tags": ["airplay", "project", "stream", "display"]}, {"name": "cc-circle-fill", "title": "CC circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["creative commons"]}, {"name": "cc-circle", "title": "CC circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["creative commons"]}, {"name": "cc-square-fill", "title": "CC square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["creative commons"]}, {"name": "cc-square", "title": "CC square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["creative commons"]}, {"name": "chat-dots-fill", "title": "Chat dots fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "typing", "comment"]}, {"name": "chat-dots", "title": "Chat dots", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "typing", "comment"]}, {"name": "chat-fill", "title": "Chat fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-heart-fill", "title": "Chat heart fill", "categories": ["Communications", "Love"], "tags": ["chat bubble", "text", "message", "valentine", "romance", "comment"]}, {"name": "chat-heart", "title": "Chat heart", "categories": ["Communications", "Love"], "tags": ["chat bubble", "text", "message", "valentine", "romance", "comment"]}, {"name": "chat-left-dots-fill", "title": "Chat left dots fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "typing", "comment"]}, {"name": "chat-left-dots", "title": "Chat left dots", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "typing", "comment"]}, {"name": "chat-left-fill", "title": "<PERSON><PERSON> left fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-left-heart-fill", "title": "<PERSON><PERSON> left heart fill", "categories": ["Communications", "Love"], "tags": ["chat bubble", "text", "message", "valentine", "romance", "comment"]}, {"name": "chat-left-heart", "title": "<PERSON><PERSON> left heart", "categories": ["Communications", "Love"], "tags": ["chat bubble", "text", "message", "valentine", "romance", "comment"]}, {"name": "chat-left-quote-fill", "title": "<PERSON><PERSON> left quote fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "quote", "comment"]}, {"name": "chat-left-quote", "title": "<PERSON><PERSON> left quote", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "quote", "comment"]}, {"name": "chat-left-text-fill", "title": "<PERSON><PERSON> left text fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-left-text", "title": "<PERSON><PERSON> left text", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-left", "title": "<PERSON><PERSON> left", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-quote-fill", "title": "Chat quote fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "quote", "comment"]}, {"name": "chat-quote", "title": "Chat quote", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "quote", "comment"]}, {"name": "chat-right-dots-fill", "title": "Chat right dots fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "typing", "comment"]}, {"name": "chat-right-dots", "title": "Chat right dots", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "typing", "comment"]}, {"name": "chat-right-fill", "title": "Chat right fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-right-heart-fill", "title": "Chat right heart fill", "categories": ["Communications", "Love"], "tags": ["chat bubble", "text", "message", "valentine", "romance", "comment"]}, {"name": "chat-right-heart", "title": "Chat right heart", "categories": ["Communications", "Love"], "tags": ["chat bubble", "text", "message", "valentine", "romance", "comment"]}, {"name": "chat-right-quote-fill", "title": "Chat right quote fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "quote", "comment"]}, {"name": "chat-right-quote", "title": "Chat right quote", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "quote", "comment"]}, {"name": "chat-right-text-fill", "title": "Chat right text fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-right-text", "title": "Chat right text", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-right", "title": "Chat right", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-square-dots-fill", "title": "Chat square dots fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "typing", "comment"]}, {"name": "chat-square-dots", "title": "Chat square dots", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "typing", "comment"]}, {"name": "chat-square-fill", "title": "Chat square fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-square-heart-fill", "title": "Chat square heart fill", "categories": ["Communications", "Love"], "tags": ["chat bubble", "text", "message", "valentine", "romance", "comment"]}, {"name": "chat-square-heart", "title": "Chat square heart", "categories": ["Communications", "Love"], "tags": ["chat bubble", "text", "message", "valentine", "romance", "comment"]}, {"name": "chat-square-quote-fill", "title": "Chat square quote fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "quote", "comment"]}, {"name": "chat-square-quote", "title": "Chat square quote", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "quote", "comment"]}, {"name": "chat-square-text-fill", "title": "Chat square text fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-square-text", "title": "Chat square text", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-square", "title": "Chat square", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-text-fill", "title": "Chat text fill", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat-text", "title": "Chat text", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "chat", "title": "Cha<PERSON>", "categories": ["Communications"], "tags": ["chat bubble", "text", "message", "comment"]}, {"name": "check-all", "title": "Check all", "categories": ["UI and keyboard"], "tags": ["checkmark", "todo", "done", "select"]}, {"name": "check-circle-fill", "title": "Check circle fill", "categories": ["Alerts, warnings, and signs"], "tags": ["checkmark", "confirm", "done"]}, {"name": "check-circle", "title": "Check circle", "categories": ["Alerts, warnings, and signs"], "tags": ["checkmark", "confirm", "done"]}, {"name": "check-lg", "title": "Check lg", "categories": ["Alerts, warnings, and signs"], "tags": ["checkmark", "confirm", "done"]}, {"name": "check-square-fill", "title": "Check square fill", "categories": ["Alerts, warnings, and signs"], "tags": ["checkmark", "confirm", "done"]}, {"name": "check-square", "title": "Check square", "categories": ["Alerts, warnings, and signs"], "tags": ["checkmark", "confirm", "done"]}, {"name": "check", "title": "Check", "categories": ["Alerts, warnings, and signs"], "tags": ["checkmark", "confirm", "done"]}, {"name": "check2-all", "title": "Check2 all", "categories": ["UI and keyboard"], "tags": ["checkmark", "todo", "select", "done", "checkbox"]}, {"name": "check2-circle", "title": "Check2 circle", "categories": ["UI and keyboard"], "tags": ["checkmark", "todo", "select", "done", "checkbox"]}, {"name": "check2-square", "title": "Check2 square", "categories": ["UI and keyboard"], "tags": ["checkmark", "todo", "select", "done", "checkbox"]}, {"name": "check2", "title": "Check2", "categories": ["UI and keyboard"], "tags": ["checkmark", "todo", "select", "done", "checkbox"]}, {"name": "chevron-bar-contract", "title": "Chevron bar contract", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-bar-down", "title": "Chevron bar down", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-bar-expand", "title": "Chevron bar expand", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-bar-left", "title": "Chevron bar left", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-bar-right", "title": "Chevron bar right", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-bar-up", "title": "Chevron bar up", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-compact-down", "title": "Chevron compact down", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-compact-left", "title": "Chevron compact left", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-compact-right", "title": "Chevron compact right", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-compact-up", "title": "Chevron compact up", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-contract", "title": "Chevron contract", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-double-down", "title": "Chevron double down", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-double-left", "title": "<PERSON><PERSON><PERSON> double left", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-double-right", "title": "Chevron double right", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-double-up", "title": "Chevron double up", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-down", "title": "Chevron down", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-expand", "title": "Chevron expand", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-left", "title": "<PERSON><PERSON><PERSON> left", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-right", "title": "Chevron right", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "chevron-up", "title": "Chevron up", "categories": ["Chevrons"], "tags": ["chevron"]}, {"name": "circle-fill", "title": "Circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape"]}, {"name": "circle-half", "title": "Circle half fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape"]}, {"name": "circle-square", "title": "Circle square", "categories": ["Graphics"], "tags": ["graphics", "vector", "merge", "layers"]}, {"name": "circle", "title": "Circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape"]}, {"name": "clipboard-check-fill", "title": "Clipboard check fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-check", "title": "Clipboard check", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-data-fill", "title": "Clipboard data fill", "categories": ["Real world"], "tags": ["copy", "paste", "data", "analytics", "graph", "chart"]}, {"name": "clipboard-data", "title": "Clipboard data", "categories": ["Real world"], "tags": ["copy", "paste", "data", "analytics", "graph", "chart"]}, {"name": "clipboard-fill", "title": "Clipboard fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-heart-fill", "title": "Clipboard heart fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-heart", "title": "Clipboard heart", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-minus-fill", "title": "Clipboard minus fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-minus", "title": "Clipboard minus", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-plus-fill", "title": "Clipboard plus fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-plus", "title": "Clipboard plus", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-pulse", "title": "Clipboard pulse", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-x-fill", "title": "Clipboard x fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard-x", "title": "Clipboard x", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard", "title": "Clipboard", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-check-fill", "title": "Clipboard2 check fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-check", "title": "Clipboard2 check", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-data-fill", "title": "Clipboard2 data fill", "categories": ["Real world"], "tags": ["copy", "paste", "data", "analytics", "graph", "chart"]}, {"name": "clipboard2-data", "title": "Clipboard2 data", "categories": ["Real world"], "tags": ["copy", "paste", "data", "analytics", "graph", "chart"]}, {"name": "clipboard2-fill", "title": "Clipboard2 fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-heart-fill", "title": "Clipboard2 heart fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-heart", "title": "Clipboard2 heart", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-minus-fill", "title": "Clipboard2 minus fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-minus", "title": "Clipboard2 minus", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-plus-fill", "title": "Clipboard2 plus fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-plus", "title": "Clipboard2 plus", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-pulse-fill", "title": "Clipboard2 pulse fill", "categories": ["Real world", "Medical"], "tags": ["copy", "paste", "heartrate"]}, {"name": "clipboard2-pulse", "title": "Clipboard2 pulse", "categories": ["Real world", "Medical"], "tags": ["copy", "paste", "heartrate"]}, {"name": "clipboard2-x-fill", "title": "Clipboard2 x fill", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2-x", "title": "Clipboard2 x", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clipboard2", "title": "Clipboard2", "categories": ["Real world"], "tags": ["copy", "paste"]}, {"name": "clock-fill", "title": "Clock fill", "categories": ["Miscellaneous"], "tags": ["time"]}, {"name": "clock-history", "title": "Clock history", "categories": ["Miscellaneous"], "tags": ["time", "history"]}, {"name": "clock", "title": "Clock", "categories": ["Miscellaneous"], "tags": ["time"]}, {"name": "cloud-arrow-down-fill", "title": "Cloud arrow down fill", "categories": ["Clouds"], "tags": ["download"]}, {"name": "cloud-arrow-down", "title": "Cloud arrow down", "categories": ["Clouds"], "tags": ["download"]}, {"name": "cloud-arrow-up-fill", "title": "Cloud arrow up fill", "categories": ["Clouds"], "tags": ["upload"]}, {"name": "cloud-arrow-up", "title": "Cloud arrow up", "categories": ["Clouds"], "tags": ["upload"]}, {"name": "cloud-check-fill", "title": "Cloud check fill", "categories": ["Clouds"], "tags": ["checkmark"]}, {"name": "cloud-check", "title": "Cloud check", "categories": ["Clouds"], "tags": ["checkmark"]}, {"name": "cloud-download-fill", "title": "Cloud download fill", "categories": ["Clouds"], "tags": ["arrow", "save"]}, {"name": "cloud-download", "title": "Cloud download", "categories": ["Clouds"], "tags": ["arrow", "save"]}, {"name": "cloud-drizzle-fill", "title": "Cloud drizzle fill", "categories": ["Weather"], "tags": ["storm", "rain"]}, {"name": "cloud-drizzle", "title": "Cloud drizzle", "categories": ["Weather"], "tags": ["storm", "rain"]}, {"name": "cloud-fill", "title": "Cloud fill", "categories": ["Clouds"], "tags": ["weather"]}, {"name": "cloud-fog-fill", "title": "Cloud fog fill", "categories": ["Weather"], "tags": ["foggy"]}, {"name": "cloud-fog", "title": "Cloud fog", "categories": ["Weather"], "tags": ["foggy"]}, {"name": "cloud-fog2-fill", "title": "Cloud fog2 fill", "categories": ["Weather"], "tags": ["foggy"]}, {"name": "cloud-fog2", "title": "Cloud fog2", "categories": ["Weather"], "tags": ["foggy"]}, {"name": "cloud-hail-fill", "title": "Cloud hail fill", "categories": ["Weather"], "tags": ["storm"]}, {"name": "cloud-hail", "title": "Cloud hail", "categories": ["Weather"], "tags": ["storm"]}, {"name": "cloud-haze-fill", "title": "Cloud haze fill", "categories": ["Weather"], "tags": ["smog"]}, {"name": "cloud-haze", "title": "Cloud haze", "categories": ["Weather"], "tags": ["smog"]}, {"name": "cloud-haze2-fill", "title": "Cloud haze2 fill", "categories": ["Weather"], "tags": ["smog"]}, {"name": "cloud-haze2", "title": "Cloud haze2", "categories": ["Weather"], "tags": ["smog"]}, {"name": "cloud-lightning-fill", "title": "Cloud lightning fill", "categories": ["Weather"], "tags": ["thunder", "storm"]}, {"name": "cloud-lightning-rain-fill", "title": "Cloud lightning rain fill", "categories": ["Weather"], "tags": ["thunder", "storm"]}, {"name": "cloud-lightning-rain", "title": "Cloud lightning rain", "categories": ["Weather"], "tags": ["thunder", "storm"]}, {"name": "cloud-lightning", "title": "Cloud lightning", "categories": ["Weather"], "tags": ["thunder", "storm"]}, {"name": "cloud-minus-fill", "title": "Cloud minus fill", "categories": ["Clouds"], "tags": ["subtract"]}, {"name": "cloud-minus", "title": "Cloud minus", "categories": ["Clouds"], "tags": ["subtract"]}, {"name": "cloud-moon-fill", "title": "Cloud moon fill", "categories": ["Weather"], "tags": ["cloudy", "overcast"]}, {"name": "cloud-moon", "title": "Cloud moon", "categories": ["Weather"], "tags": ["cloudy", "overcast"]}, {"name": "cloud-plus-fill", "title": "Cloud plus fill", "categories": ["Clouds"], "tags": ["add", "new", "upload"]}, {"name": "cloud-plus", "title": "Cloud plus", "categories": ["Clouds"], "tags": ["add", "new", "upload"]}, {"name": "cloud-rain-fill", "title": "Cloud rain fill", "categories": ["Weather"], "tags": ["cloud", "rainstorm", "storm"]}, {"name": "cloud-rain-heavy-fill", "title": "Cloud rain heavy fill", "categories": ["Weather"], "tags": ["cloud", "rainstorm", "storm"]}, {"name": "cloud-rain-heavy", "title": "Cloud rain heavy", "categories": ["Weather"], "tags": ["cloud", "rainstorm", "storm"]}, {"name": "cloud-rain", "title": "Cloud rain", "categories": ["Weather"], "tags": ["cloud", "rainstorm", "storm"]}, {"name": "cloud-slash-fill", "title": "Cloud slash fill", "categories": ["Clouds"], "tags": ["cloud"]}, {"name": "cloud-slash", "title": "Cloud slash", "categories": ["Clouds"], "tags": ["cloud"]}, {"name": "cloud-sleet-fill", "title": "Cloud sleet fill", "categories": ["Weather"], "tags": ["cloud", "blizzard", "flurries"]}, {"name": "cloud-sleet", "title": "Cloud sleet", "categories": ["Weather"], "tags": ["cloud", "blizzard", "flurries"]}, {"name": "cloud-snow-fill", "title": "Cloud snow fill", "categories": ["Weather"], "tags": ["cloud", "blizzard", "flurries"]}, {"name": "cloud-snow", "title": "Cloud snow", "categories": ["Weather"], "tags": ["cloud", "blizzard", "flurries"]}, {"name": "cloud-sun-fill", "title": "Cloud sun fill", "categories": ["Weather"], "tags": ["cloudy", "overcast"]}, {"name": "cloud-sun", "title": "Cloud sun", "categories": ["Weather"], "tags": ["cloudy", "overcast"]}, {"name": "cloud-upload-fill", "title": "Cloud upload fill", "categories": ["Clouds"], "tags": ["cloud"]}, {"name": "cloud-upload", "title": "Cloud upload", "categories": ["Clouds"], "tags": ["cloud"]}, {"name": "cloud", "title": "Cloud", "categories": ["Clouds"], "tags": ["weather"]}, {"name": "clouds-fill", "title": "Clouds fill", "categories": ["Weather"], "tags": ["clouds", "overcast"]}, {"name": "clouds", "title": "Clouds", "categories": ["Weather"], "tags": ["clouds", "overcast"]}, {"name": "cloudy-fill", "title": "Cloudy fill", "categories": ["Weather"], "tags": ["clouds", "overcast"]}, {"name": "cloudy", "title": "Cloudy", "categories": ["Weather"], "tags": ["clouds", "overcast"]}, {"name": "code-slash", "title": "Code slash", "categories": ["Typography"], "tags": ["text", "type", "code", "developer", "development", "software"]}, {"name": "code-square", "title": "Code square", "categories": ["Typography"], "tags": ["text", "type", "code", "developer", "development", "software", "preformatted"]}, {"name": "code", "title": "Code", "categories": ["Typography"], "tags": ["text", "type", "code", "developer", "development", "software"]}, {"name": "coin", "title": "Coin", "categories": ["Commerce"], "tags": ["money", "finance", "banking", "currency"]}, {"name": "collection-fill", "title": "Collection fill", "categories": ["Media"], "tags": ["library", "group"]}, {"name": "collection-play-fill", "title": "Collection play fill", "categories": ["Media"], "tags": ["library", "group", "play"]}, {"name": "collection-play", "title": "Collection play", "categories": ["Media"], "tags": ["library", "group", "play"]}, {"name": "collection", "title": "Collection", "categories": ["Media"], "tags": ["library", "group"]}, {"name": "columns-gap", "title": "Columns gap", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "columns", "title": "Columns", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "command", "title": "Command", "categories": ["UI and keyboard"], "tags": ["key", "mac"]}, {"name": "compass-fill", "title": "Compass fill", "categories": ["Geo"], "tags": ["direction", "map", "location"]}, {"name": "compass", "title": "<PERSON>mp<PERSON>", "categories": ["Geo"], "tags": ["direction", "map", "location"]}, {"name": "cone-striped", "title": "Cone striped", "categories": ["Real world"], "tags": ["construction", "warning", "safety"]}, {"name": "cone", "title": "Cone", "categories": ["Real world"], "tags": ["construction", "warning", "safety"]}, {"name": "controller", "title": "Controller", "categories": ["Devices"], "tags": ["game", "gaming", "video-game"]}, {"name": "cookie", "title": "<PERSON><PERSON>", "categories": ["Real World"], "tags": ["dessert"]}, {"name": "copy", "title": "Copy", "categories": ["UI and keyboard"], "tags": ["paste", "clone", "cut", "duplicate"]}, {"name": "cpu-fill", "title": "Cpu fill", "categories": ["Devices"], "tags": ["processor", "chip", "computer"]}, {"name": "cpu", "title": "CPU", "categories": ["Devices"], "tags": ["processor", "chip", "computer"]}, {"name": "credit-card-2-back-fill", "title": "Credit card 2 back fill", "categories": ["Real world"], "tags": ["debit", "card", "payment"]}, {"name": "credit-card-2-back", "title": "Credit card 2 back", "categories": ["Real world"], "tags": ["debit", "card", "payment"]}, {"name": "credit-card-2-front-fill", "title": "Credit card 2 front fill", "categories": ["Real world"], "tags": ["debit", "card", "payment"]}, {"name": "credit-card-2-front", "title": "Credit card 2 front", "categories": ["Real world"], "tags": ["debit", "card", "payment"]}, {"name": "credit-card-fill", "title": "Credit card fill", "categories": ["Real world"], "tags": ["debit", "card", "payment"]}, {"name": "credit-card", "title": "Credit card", "categories": ["Real world"], "tags": ["debit", "card", "payment"]}, {"name": "crop", "title": "Crop", "categories": ["Graphics"], "tags": ["crop"]}, {"name": "crosshair", "title": "<PERSON><PERSON><PERSON>", "categories": ["Geo"], "tags": ["geography", "map", "pin", "location"]}, {"name": "crosshair2", "title": "Crosshair2", "categories": ["Geo"], "tags": ["geography", "map", "pin", "location"]}, {"name": "cup-fill", "title": "Cup fill", "categories": ["Real world"], "tags": ["mug"]}, {"name": "cup-hot-fill", "title": "Cup hot fill", "categories": ["Real world"], "tags": ["mug", "steam", "coffee", "tea"]}, {"name": "cup-hot", "title": "Cup hot", "categories": ["Real world"], "tags": ["mug", "steam", "coffee", "tea"]}, {"name": "cup-straw", "title": "Cup straw", "categories": ["Real world"], "tags": ["mug", "glass", "drink"]}, {"name": "cup", "title": "Cup", "categories": ["Real world"], "tags": ["mug"]}, {"name": "currency-bitcoin", "title": "Currency bitcoin", "categories": ["Commerce"], "tags": ["money", "finance", "crypto"]}, {"name": "currency-dollar", "title": "Currency dollar", "categories": ["Commerce"], "tags": ["money", "finance", "usd"]}, {"name": "currency-euro", "title": "Currency euro", "categories": ["Commerce"], "tags": ["money", "finance"]}, {"name": "currency-exchange", "title": "Currency exchange", "categories": ["Commerce"], "tags": ["money", "finance"]}, {"name": "currency-pound", "title": "Currency pound", "categories": ["Commerce"], "tags": ["money", "finance"]}, {"name": "currency-rupee", "title": "Currency rupee", "categories": ["Commerce"], "tags": ["money", "finance"]}, {"name": "currency-yen", "title": "Currency yen", "categories": ["Commerce"], "tags": ["money", "finance"]}, {"name": "cursor-fill", "title": "Cursor fill", "categories": ["Geo"], "tags": ["pointer"]}, {"name": "cursor-text", "title": "Cursor text", "categories": ["Typography"], "tags": ["text", "type", "cursor"]}, {"name": "cursor", "title": "<PERSON><PERSON><PERSON>", "categories": ["Geo"], "tags": ["pointer"]}, {"name": "dash-circle-dotted", "title": "Dash circle dotted", "categories": ["Alerts, warnings, and signs"], "tags": ["minus"]}, {"name": "dash-circle-fill", "title": "Dash circle fill", "categories": ["Alerts, warnings, and signs"], "tags": ["minus"]}, {"name": "dash-circle", "title": "Dash circle", "categories": ["Alerts, warnings, and signs"], "tags": ["minus"]}, {"name": "dash-lg", "title": "Dash lg", "categories": ["Alerts, warnings, and signs"], "tags": ["minus"]}, {"name": "dash-square-dotted", "title": "Dash square dotted", "categories": ["Alerts, warnings, and signs"], "tags": ["minus"]}, {"name": "dash-square-fill", "title": "Dash square fill", "categories": ["Alerts, warnings, and signs"], "tags": ["minus"]}, {"name": "dash-square", "title": "Dash square", "categories": ["Alerts, warnings, and signs"], "tags": ["minus"]}, {"name": "dash", "title": "Dash", "categories": ["Alerts, warnings, and signs"], "tags": ["minus"]}, {"name": "database-add", "title": "Database add", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-check", "title": "Database check", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-dash", "title": "Database dash", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-down", "title": "Database down", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-exclamation", "title": "Database exclamation", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill-add", "title": "Database fill add", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill-check", "title": "Database fill check", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill-dash", "title": "Database fill dash", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill-down", "title": "Database fill down", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill-exclamation", "title": "Database fill exclamation", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill-gear", "title": "Database fill gear", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill-lock", "title": "Database fill lock", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill-slash", "title": "Database fill slash", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill-up", "title": "Database fill up", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill-x", "title": "Database fill x", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-fill", "title": "Database fill", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-gear", "title": "Database gear", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-lock", "title": "Database lock", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-slash", "title": "Database slash", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-up", "title": "Database up", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database-x", "title": "Database x", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "database", "title": "Database", "categories": ["Devices"], "tags": ["server", "data"]}, {"name": "device-hdd-fill", "title": "Device HDD fill", "categories": ["Devices"], "tags": ["drive", "hard drive"]}, {"name": "device-hdd", "title": "Device HDD", "categories": ["Devices"], "tags": ["drive", "hard drive"]}, {"name": "device-ssd-fill", "title": "<PERSON>ce SSD fill", "categories": ["Devices"], "tags": ["solid state", "drive"]}, {"name": "device-ssd", "title": "Device SSD", "categories": ["Devices"], "tags": ["solid state", "drive"]}, {"name": "diagram-2-fill", "title": "Diagram 2 fill", "categories": ["Graphics"], "tags": ["node", "diagram", "sitemap", "children", "org chart"]}, {"name": "diagram-2", "title": "Diagram 2", "categories": ["Graphics"], "tags": ["node", "diagram", "sitemap", "children", "org chart"]}, {"name": "diagram-3-fill", "title": "Diagram 3 fill", "categories": ["Graphics"], "tags": ["node", "diagram", "sitemap", "children", "org chart"]}, {"name": "diagram-3", "title": "Diagram 3", "categories": ["Graphics"], "tags": ["node", "diagram", "sitemap", "children", "org chart"]}, {"name": "diamond-fill", "title": "Diamond fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape"]}, {"name": "diamond-half", "title": "Diamond half fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape"]}, {"name": "diamond", "title": "Diamond", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape"]}, {"name": "dice-1-fill", "title": "Dice 1 fill", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-1", "title": "Dice 1", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-2-fill", "title": "Dice 2 fill", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-2", "title": "Dice 2", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-3-fill", "title": "Dice 3 fill", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-3", "title": "Dice 3", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-4-fill", "title": "Dice 4 fill", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-4", "title": "Dice 4", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-5-fill", "title": "Dice 5 fill", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-5", "title": "Dice 5", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-6-fill", "title": "<PERSON>ce 6 fill", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "dice-6", "title": "Dice 6", "categories": ["Entertainment"], "tags": ["dice", "die", "games", "gaming", "gambling"]}, {"name": "disc-fill", "title": "Disc fill", "categories": ["Media"], "tags": ["cd", "compact disc", "bluray", "dvd"]}, {"name": "disc", "title": "Disc", "categories": ["Media"], "tags": ["cd", "compact disc", "bluray", "dvd"]}, {"name": "discord", "title": "Discord", "categories": ["Brand"], "tags": ["social", "chat"]}, {"name": "display-fill", "title": "Display fill", "categories": ["Devices"], "tags": ["monitor", "external"]}, {"name": "display", "title": "Display", "categories": ["Devices"], "tags": ["monitor", "external"]}, {"name": "displayport-fill", "title": "DisplayPort fill", "categories": ["Devices"], "tags": ["video", "input"]}, {"name": "displayport", "title": "DisplayPort", "categories": ["Devices"], "tags": ["video", "input"]}, {"name": "distribute-horizontal", "title": "Distribute horizontal", "categories": ["Graphics"], "tags": ["space", "align"]}, {"name": "distribute-vertical", "title": "Distribute vertical", "categories": ["Graphics"], "tags": ["space", "align"]}, {"name": "door-closed-fill", "title": "Door closed fill", "categories": ["Real world"], "tags": ["door", "logout", "signout"]}, {"name": "door-closed", "title": "Door closed", "categories": ["Real world"], "tags": ["door", "logout", "signout"]}, {"name": "door-open-fill", "title": "Door open fill", "categories": ["Real world"], "tags": ["door", "login", "signin"]}, {"name": "door-open", "title": "Door open", "categories": ["Real world"], "tags": ["door", "login", "signin"]}, {"name": "dot", "title": "Dot", "categories": ["UI and keyboard"], "tags": ["middot"]}, {"name": "download", "title": "Download", "categories": ["Miscellaneous"], "tags": ["arrow", "network", "save"]}, {"name": "dpad-fill", "title": "Dpad fill", "categories": ["Entertainment"], "tags": ["gaming", "controller", "direction"]}, {"name": "dpad", "title": "Dpad", "categories": ["Entertainment"], "tags": ["gaming", "controller", "direction"]}, {"name": "dribbble", "title": "<PERSON><PERSON><PERSON>", "categories": ["Brand"], "tags": ["social"]}, {"name": "dropbox", "title": "Dropbox", "categories": ["Brand"], "tags": ["dropbox"]}, {"name": "droplet-fill", "title": "Droplet fill", "categories": ["Graphics"], "tags": ["water-drop", "paint", "ink", "liquid"]}, {"name": "droplet-half", "title": "Droplet half", "categories": ["Graphics"], "tags": ["water-drop", "paint", "ink", "liquid"]}, {"name": "droplet", "title": "Droplet", "categories": ["Graphics"], "tags": ["water-drop", "paint", "ink", "liquid"]}, {"name": "duffle-fill", "title": "<PERSON>le fill", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "duffle", "title": "<PERSON><PERSON>", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "ear-fill", "title": "Ear fill", "categories": ["Real World"], "tags": ["hearing", "sound", "listen"]}, {"name": "ear", "title": "Ear", "categories": ["Real World"], "tags": ["hearing", "sound", "listen"]}, {"name": "earbuds", "title": "Earbuds", "categories": ["Devices"], "tags": ["headphones"]}, {"name": "easel-fill", "title": "Easel fill", "categories": ["Graphics"], "tags": ["paint", "draw", "art", "present"]}, {"name": "easel", "title": "<PERSON><PERSON><PERSON>", "categories": ["Graphics"], "tags": ["paint", "draw", "art", "present"]}, {"name": "easel2-fill", "title": "Easel2 fill", "categories": ["Graphics"], "tags": ["paint", "draw", "art", "present"]}, {"name": "easel2", "title": "Easel2", "categories": ["Graphics"], "tags": ["paint", "draw", "art", "present"]}, {"name": "easel3-fill", "title": "Easel3 fill", "categories": ["Graphics"], "tags": ["paint", "draw", "art", "present"]}, {"name": "easel3", "title": "Easel3", "categories": ["Graphics"], "tags": ["paint", "draw", "art", "present"]}, {"name": "egg-fill", "title": "Egg fill", "categories": ["Real world"], "tags": ["food"]}, {"name": "egg-fried", "title": "Egg fried", "categories": ["Real world"], "tags": ["food"]}, {"name": "egg", "title": "Egg", "categories": ["Real world"], "tags": ["food"]}, {"name": "eject-fill", "title": "Eject fill", "categories": ["UI and keyboard"], "tags": ["disc", "cd", "dvd"]}, {"name": "eject", "title": "Eject", "categories": ["UI and keyboard"], "tags": ["disc", "cd", "dvd"]}, {"name": "emoji-angry-fill", "title": "Emoji angry fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "anger", "upset"]}, {"name": "emoji-angry", "title": "<PERSON><PERSON><PERSON> angry", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "anger", "upset"]}, {"name": "emoji-astonished-fill", "title": "<PERSON><PERSON><PERSON> astonished fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "surprised"]}, {"name": "emoji-astonished", "title": "<PERSON><PERSON><PERSON> astonished", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "surprised"]}, {"name": "emoji-dizzy-fill", "title": "Emoji dizzy fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-dizzy", "title": "Emoji dizzy", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-expressionless-fill", "title": "Emoji expressionless fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "neutral", "unphased"]}, {"name": "emoji-expressionless", "title": "Emoji expressionless", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "neutral", "unphased"]}, {"name": "emoji-frown-fill", "title": "Emoji frown fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "sad"]}, {"name": "emoji-frown", "title": "<PERSON><PERSON>ji frown", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "sad"]}, {"name": "emoji-grimace-fill", "title": "Emoji grimace fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-grimace", "title": "Em<PERSON>ji grimace", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-grin-fill", "title": "Emoji grin fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-grin", "title": "Emoji grin", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-heart-eyes-fill", "title": "Emoji heart eyes fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "heart", "love"]}, {"name": "emoji-heart-eyes", "title": "Emoji heart eyes", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "heart", "love"]}, {"name": "emoji-kiss-fill", "title": "Emoji kiss fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "heart", "love"]}, {"name": "emoji-kiss", "title": "Emoji kiss", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "heart", "love"]}, {"name": "emoji-laughing-fill", "title": "Emoji laughing fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "happy"]}, {"name": "emoji-laughing", "title": "<PERSON><PERSON><PERSON> laughing", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "happy"]}, {"name": "emoji-neutral-fill", "title": "Emoji neutral fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "expressionless"]}, {"name": "emoji-neutral", "title": "<PERSON><PERSON>ji neutral", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "expressionless"]}, {"name": "emoji-smile-fill", "title": "Emoji smile fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "happy"]}, {"name": "emoji-smile-upside-down-fill", "title": "Emoji smile upside down fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "sarcasm"]}, {"name": "emoji-smile-upside-down", "title": "Emoji smile upside down", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "sarcasm"]}, {"name": "emoji-smile", "title": "Emoji smile", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "happy"]}, {"name": "emoji-sunglasses-fill", "title": "Emoji sunglasses fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "cool"]}, {"name": "emoji-sunglasses", "title": "Emoji sunglasses", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon", "cool"]}, {"name": "emoji-surprise-fill", "title": "Emoji surprise fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-surprise", "title": "Emoji surprise", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-tear-fill", "title": "Emoji tear fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-tear", "title": "Emoji tear", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-wink-fill", "title": "Emoji wink fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "emoji-wink", "title": "Emoji wink", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["emoticon"]}, {"name": "envelope-arrow-down-fill", "title": "Envelope arrow down fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-arrow-down", "title": "Envelope arrow down", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-arrow-up-fill", "title": "Envelope arrow up fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-arrow-up", "title": "Envelope arrow up", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-at-fill", "title": "Envelope at fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-at", "title": "Envelope at", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-check-fill", "title": "Envelope check fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-check", "title": "Envelope check", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-dash-fill", "title": "Envelope dash fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-dash", "title": "Envelope dash", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-exclamation-fill", "title": "Envelope exclamation fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-exclamation", "title": "Envelope exclamation", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-fill", "title": "Envelope fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-heart-fill", "title": "Envelope heart fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter", "love", "valentine", "romance"]}, {"name": "envelope-heart", "title": "Envelope heart", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter", "love", "valentine", "romance"]}, {"name": "envelope-open-fill", "title": "Envelope open fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-open-heart-fill", "title": "Envelope open heart fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter", "love", "valentine", "romance"]}, {"name": "envelope-open-heart", "title": "Envelope open heart", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter", "love", "valentine", "romance"]}, {"name": "envelope-open", "title": "Envelope open", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-paper-fill", "title": "Envelope paper fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-paper-heart-fill", "title": "Envelope paper heart fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter", "love", "valentine", "romance"]}, {"name": "envelope-paper-heart", "title": "Envelope paper heart", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter", "love", "valentine", "romance"]}, {"name": "envelope-paper", "title": "Envelope paper", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-plus-fill", "title": "Envelope plus fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-plus", "title": "Envelope plus", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-slash-fill", "title": "Envelope slash fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-slash", "title": "Envelope slash", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-x-fill", "title": "Envelope x fill", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope-x", "title": "Envelope x", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "envelope", "title": "Envelope", "categories": ["Communications"], "tags": ["email", "message", "mail", "letter"]}, {"name": "eraser-fill", "title": "Eraser fill", "categories": ["Graphics"], "tags": ["erase", "remove"]}, {"name": "eraser", "title": "Eraser", "categories": ["Graphics"], "tags": ["erase", "remove"]}, {"name": "escape", "title": "Escape", "categories": ["UI and Keyboard"], "tags": ["esc", "quit", "exit"]}, {"name": "ethernet", "title": "Ethernet", "categories": ["Devices"], "tags": ["internet", "connection", "port", "plug"]}, {"name": "ev-front-fill", "title": "Ev front fill", "categories": ["Transportation"], "tags": ["car", "automobile", "automotive", "auto", "sedan", "drive", "driving", "electric vehicle", "charging"]}, {"name": "ev-front", "title": "Ev front", "categories": ["Transportation"], "tags": ["car", "automobile", "automotive", "auto", "sedan", "drive", "driving", "electric vehicle", "charging"]}, {"name": "ev-station-fill", "title": "EV station fill", "categories": ["Transportation"], "tags": ["charging", "electric vehicle"]}, {"name": "ev-station", "title": "EV station", "categories": ["Transportation"], "tags": ["charging", "electric vehicle"]}, {"name": "exclamation-circle-fill", "title": "Exclamation circle fill", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation-circle", "title": "Exclamation circle", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation-diamond-fill", "title": "Exclamation diamond fill", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation-diamond", "title": "Exclamation diamond", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation-lg", "title": "Exclamation lg", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation-octagon-fill", "title": "Exclamation octagon fill", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation-octagon", "title": "Exclamation octagon", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation-square-fill", "title": "Exclamation square fill", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation-square", "title": "Exclamation square", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation-triangle-fill", "title": "Exclamation triangle fill", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation-triangle", "title": "Exclamation triangle", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclamation", "title": "Exclamation", "categories": ["Alerts, warnings, and signs"], "tags": ["alert", "warning"]}, {"name": "exclude", "title": "Exclude", "categories": ["Graphics"], "tags": ["graphics", "vector", "merge", "layers"]}, {"name": "explicit-fill", "title": "Explicit fill", "categories": ["Badges"], "tags": ["r18"]}, {"name": "explicit", "title": "Explicit", "categories": ["Badges"], "tags": ["r18"]}, {"name": "exposure", "title": "Exposure", "categories": ["Graphics"], "tags": ["photo", "editing", "image", "picture"]}, {"name": "eye-fill", "title": "Eye fill", "categories": ["Real world"], "tags": ["eyeball", "look", "see"]}, {"name": "eye-slash-fill", "title": "Eye slash fill", "categories": ["Real world"], "tags": ["eyeball", "look", "see"]}, {"name": "eye-slash", "title": "Eye slash", "categories": ["Real world"], "tags": ["eyeball", "look", "see"]}, {"name": "eye", "title": "Eye", "categories": ["Real world"], "tags": ["eyeball", "look", "see"]}, {"name": "eyedropper", "title": "Eyedropper", "categories": ["Graphics"], "tags": ["color", "picker"]}, {"name": "eyeglasses", "title": "Eyeglasses", "categories": ["Real world"], "tags": ["eyeball", "look", "see", "glasses", "reading"]}, {"name": "facebook", "title": "Facebook", "categories": ["Brand"], "tags": ["social"]}, {"name": "fan", "title": "Fan", "categories": ["Real World"], "tags": ["fan", "vent", "airflow"]}, {"name": "fast-forward-btn-fill", "title": "Fast forward btn fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "fast-forward-btn", "title": "Fast forward btn", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "fast-forward-circle-fill", "title": "Fast forward circle fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "fast-forward-circle", "title": "Fast forward circle", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "fast-forward-fill", "title": "Fast forward fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "fast-forward", "title": "Fast forward", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "feather", "title": "<PERSON><PERSON>", "categories": ["Real World"], "tags": ["bird", "flight", "light"]}, {"name": "feather2", "title": "Feather2", "categories": ["Real World"], "tags": ["bird", "flight", "light"]}, {"name": "file-arrow-down-fill", "title": "File arrow down fill", "categories": ["Files and folders"], "tags": ["doc", "document", "download"]}, {"name": "file-arrow-down", "title": "File arrow down", "categories": ["Files and folders"], "tags": ["doc", "document", "download"]}, {"name": "file-arrow-up-fill", "title": "File arrow up fill", "categories": ["Files and folders"], "tags": ["doc", "document", "upload"]}, {"name": "file-arrow-up", "title": "File arrow up", "categories": ["Files and folders"], "tags": ["doc", "document", "upload"]}, {"name": "file-bar-graph-fill", "title": "File bar graph fill", "categories": ["Files and folders"], "tags": ["doc", "document", "data", "chart"]}, {"name": "file-bar-graph", "title": "File bar graph", "categories": ["Files and folders"], "tags": ["doc", "document", "data", "chart"]}, {"name": "file-binary-fill", "title": "File binary fill", "categories": ["Files and folders"], "tags": ["doc", "document", "binary", "source"]}, {"name": "file-binary", "title": "File binary", "categories": ["Files and folders"], "tags": ["doc", "document", "binary", "source"]}, {"name": "file-break-fill", "title": "File break fill", "categories": ["Files and folders"], "tags": ["doc", "document", "page-break"]}, {"name": "file-break", "title": "File break", "categories": ["Files and folders"], "tags": ["doc", "document", "page-break"]}, {"name": "file-check-fill", "title": "File check fill", "categories": ["Files and folders"], "tags": ["doc", "document", "check", "verified"]}, {"name": "file-check", "title": "File check", "categories": ["Files and folders"], "tags": ["doc", "document", "check", "verified"]}, {"name": "file-code-fill", "title": "File code fill", "categories": ["Files and folders"], "tags": ["doc", "document", "code", "development"]}, {"name": "file-code", "title": "File code", "categories": ["Files and folders"], "tags": ["doc", "document", "code", "development"]}, {"name": "file-diff-fill", "title": "File diff fill", "categories": ["Files and folders"], "tags": ["doc", "document", "version", "development"]}, {"name": "file-diff", "title": "File diff", "categories": ["Files and folders"], "tags": ["doc", "document", "version", "development"]}, {"name": "file-earmark-arrow-down-fill", "title": "File earmark arrow down fill", "categories": ["Files and folders"], "tags": ["doc", "document", "download"]}, {"name": "file-earmark-arrow-down", "title": "File earmark arrow down", "categories": ["Files and folders"], "tags": ["doc", "document", "download"]}, {"name": "file-earmark-arrow-up-fill", "title": "File earmark arrow up fill", "categories": ["Files and folders"], "tags": ["doc", "document", "upload"]}, {"name": "file-earmark-arrow-up", "title": "File earmark arrow up", "categories": ["Files and folders"], "tags": ["doc", "document", "upload"]}, {"name": "file-earmark-bar-graph-fill", "title": "File earmark bar graph fill", "categories": ["Files and folders"], "tags": ["doc", "document", "data", "chart"]}, {"name": "file-earmark-bar-graph", "title": "File earmark bar graph", "categories": ["Files and folders"], "tags": ["doc", "document", "data", "chart"]}, {"name": "file-earmark-binary-fill", "title": "File earmark binary fill", "categories": ["Files and folders"], "tags": ["doc", "document", "binary", "source"]}, {"name": "file-earmark-binary", "title": "File earmark binary", "categories": ["Files and folders"], "tags": ["doc", "document", "binary", "source"]}, {"name": "file-earmark-break-fill", "title": "File earmark break fill", "categories": ["Files and folders"], "tags": ["doc", "document", "page-break"]}, {"name": "file-earmark-break", "title": "File earmark break", "categories": ["Files and folders"], "tags": ["doc", "document", "page-break"]}, {"name": "file-earmark-check-fill", "title": "File earmark check fill", "categories": ["Files and folders"], "tags": ["doc", "document", "check", "verified"]}, {"name": "file-earmark-check", "title": "File earmark check", "categories": ["Files and folders"], "tags": ["doc", "document", "check", "verified"]}, {"name": "file-earmark-code-fill", "title": "File earmark code fill", "categories": ["Files and folders"], "tags": ["doc", "document", "code", "development"]}, {"name": "file-earmark-code", "title": "File earmark code", "categories": ["Files and folders"], "tags": ["doc", "document", "code", "development"]}, {"name": "file-earmark-diff-fill", "title": "File earmark diff fill", "categories": ["Files and folders"], "tags": ["doc", "document", "version", "development"]}, {"name": "file-earmark-diff", "title": "File earmark diff", "categories": ["Files and folders"], "tags": ["doc", "document", "version", "development"]}, {"name": "file-earmark-easel-fill", "title": "File earmark easel fill", "categories": ["Files and folders"], "tags": ["slides", "presentation", "powerpoint", "keynote"]}, {"name": "file-earmark-easel", "title": "File earmark easel", "categories": ["Files and folders"], "tags": ["slides", "presentation", "powerpoint", "keynote"]}, {"name": "file-earmark-excel-fill", "title": "File earmark excel fill", "categories": ["Files and folders"], "tags": ["doc", "document", "spreadsheet", "excel", "table"]}, {"name": "file-earmark-excel", "title": "File earmark excel", "categories": ["Files and folders"], "tags": ["doc", "document", "spreadsheet", "excel", "table"]}, {"name": "file-earmark-fill", "title": "File earmark fill", "categories": ["Files and folders"], "tags": ["doc", "document", "page"]}, {"name": "file-earmark-font-fill", "title": "File earmark font fill", "categories": ["Files and folders"], "tags": ["ttf", "otf"]}, {"name": "file-earmark-font", "title": "File earmark font", "categories": ["Files and folders"], "tags": ["ttf", "otf"]}, {"name": "file-earmark-image-fill", "title": "File earmark image fill", "categories": ["Files and folders"], "tags": ["photo", "picture"]}, {"name": "file-earmark-image", "title": "File earmark image", "categories": ["Files and folders"], "tags": ["photo", "picture"]}, {"name": "file-earmark-lock-fill", "title": "File earmark lock fill", "categories": ["Files and folders"], "tags": ["lock", "private", "secure"]}, {"name": "file-earmark-lock", "title": "File earmark lock", "categories": ["Files and folders"], "tags": ["lock", "private", "secure"]}, {"name": "file-earmark-lock2-fill", "title": "File earmark lock2 fill", "categories": ["Files and folders"], "tags": ["lock", "private", "secure"]}, {"name": "file-earmark-lock2", "title": "File earmark lock2", "categories": ["Files and folders"], "tags": ["lock", "private", "secure"]}, {"name": "file-earmark-medical-fill", "title": "File earmark medical fill", "categories": ["Files and folders"], "tags": ["doc", "document", "medical", "hospital", "health"]}, {"name": "file-earmark-medical", "title": "File earmark medical", "categories": ["Files and folders"], "tags": ["doc", "document", "medical", "hospital", "health"]}, {"name": "file-earmark-minus-fill", "title": "File earmark minus fill", "categories": ["Files and folders"], "tags": ["doc", "document", "delete", "remove"]}, {"name": "file-earmark-minus", "title": "File earmark minus", "categories": ["Files and folders"], "tags": ["doc", "document", "delete", "remove"]}, {"name": "file-earmark-music-fill", "title": "File earmark music fill", "categories": ["Files and folders"], "tags": ["doc", "document", "music", "audio", "playlist", "songs"]}, {"name": "file-earmark-music", "title": "File earmark music", "categories": ["Files and folders"], "tags": ["doc", "document", "music", "audio", "playlist", "songs"]}, {"name": "file-earmark-pdf-fill", "title": "File earmark PDF fill", "categories": ["Files and folders"], "tags": ["doc", "document", "adobe", "acrobat"]}, {"name": "file-earmark-pdf", "title": "File earmark PDF", "categories": ["Files and folders"], "tags": ["doc", "document", "adobe", "acrobat"]}, {"name": "file-earmark-person-fill", "title": "File earmark person fill", "categories": ["Files and folders"], "tags": ["doc", "document", "personal", "cv", "resume", "about"]}, {"name": "file-earmark-person", "title": "File earmark person", "categories": ["Files and folders"], "tags": ["doc", "document", "personal", "cv", "resume", "about"]}, {"name": "file-earmark-play-fill", "title": "File earmark play fill", "categories": ["Files and folders"], "tags": ["video", "present"]}, {"name": "file-earmark-play", "title": "File earmark play", "categories": ["Files and folders"], "tags": ["video", "present"]}, {"name": "file-earmark-plus-fill", "title": "File earmark plus fill", "categories": ["Files and folders"], "tags": ["doc", "document", "add", "new"]}, {"name": "file-earmark-plus", "title": "File earmark plus", "categories": ["Files and folders"], "tags": ["doc", "document", "add", "new"]}, {"name": "file-earmark-post-fill", "title": "File earmark post fill", "categories": ["Files and folders"], "tags": ["doc", "document", "post"]}, {"name": "file-earmark-post", "title": "File earmark post", "categories": ["Files and folders"], "tags": ["doc", "document", "post"]}, {"name": "file-earmark-ppt-fill", "title": "File earmark ppt fill", "categories": ["Files and folders"], "tags": ["slides", "presentation", "powerpoint", "keynote"]}, {"name": "file-earmark-ppt", "title": "File earmark ppt", "categories": ["Files and folders"], "tags": ["slides", "presentation", "powerpoint", "keynote"]}, {"name": "file-earmark-richtext-fill", "title": "File earmark richtext fill", "categories": ["Files and folders"], "tags": ["text", "doc", "document"]}, {"name": "file-earmark-richtext", "title": "File earmark richtext", "categories": ["Files and folders"], "tags": ["text", "doc", "document"]}, {"name": "file-earmark-ruled-fill", "title": "File earmark ruled fill", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "file-earmark-ruled", "title": "File earmark ruled", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "file-earmark-slides-fill", "title": "File earmark slides fill", "categories": ["Files and folders"], "tags": ["presentation", "keynote", "powerpoint"]}, {"name": "file-earmark-slides", "title": "File earmark slides", "categories": ["Files and folders"], "tags": ["presentation", "keynote", "powerpoint"]}, {"name": "file-earmark-spreadsheet-fill", "title": "File earmark spreadsheet fill", "categories": ["Files and folders"], "tags": ["doc", "document", "excel", "table"]}, {"name": "file-earmark-spreadsheet", "title": "File earmark spreadsheet", "categories": ["Files and folders"], "tags": ["doc", "document", "excel", "table"]}, {"name": "file-earmark-text-fill", "title": "File earmark text fill", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "file-earmark-text", "title": "File earmark text", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "file-earmark-word-fill", "title": "File earmark word fill", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "file-earmark-word", "title": "File earmark word", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "file-earmark-x-fill", "title": "File earmark x fill", "categories": ["Files and folders"], "tags": ["document", "remove", "delete"]}, {"name": "file-earmark-x", "title": "File earmark x", "categories": ["Files and folders"], "tags": ["document", "remove", "delete"]}, {"name": "file-earmark-zip-fill", "title": "File earmark zip fill", "categories": ["Files and folders"], "tags": ["doc", "document", "zip", "archive", "compress"]}, {"name": "file-earmark-zip", "title": "File earmark zip", "categories": ["Files and folders"], "tags": ["doc", "document", "zip", "archive", "compress"]}, {"name": "file-earmark", "title": "File earmark", "categories": ["Files and folders"], "tags": ["doc", "document", "page"]}, {"name": "file-easel-fill", "title": "File easel fill", "categories": ["Files and folders"], "tags": ["slides", "presentation", "powerpoint", "keynote"]}, {"name": "file-easel", "title": "File easel", "categories": ["Files and folders"], "tags": ["slides", "presentation", "powerpoint", "keynote"]}, {"name": "file-excel-fill", "title": "File excel fill", "categories": ["Files and folders"], "tags": ["doc", "document", "spreadsheet", "excel", "table"]}, {"name": "file-excel", "title": "File excel", "categories": ["Files and folders"], "tags": ["doc", "document", "spreadsheet", "excel", "table"]}, {"name": "file-fill", "title": "File fill", "categories": ["Files and folders"], "tags": ["doc", "document", "page"]}, {"name": "file-font-fill", "title": "File font fill", "categories": ["Files and folders"], "tags": ["ttf", "otf"]}, {"name": "file-font", "title": "File font", "categories": ["Files and folders"], "tags": ["ttf", "otf"]}, {"name": "file-image-fill", "title": "File image fill", "categories": ["Files and folders"], "tags": ["photo", "picture"]}, {"name": "file-image", "title": "File image", "categories": ["Files and folders"], "tags": ["photo", "picture"]}, {"name": "file-lock-fill", "title": "File lock fill", "categories": ["Files and folders"], "tags": ["lock", "private", "secure"]}, {"name": "file-lock", "title": "File lock", "categories": ["Files and folders"], "tags": ["lock", "private", "secure"]}, {"name": "file-lock2-fill", "title": "File lock2 fill", "categories": ["Files and folders"], "tags": ["lock", "private", "secure"]}, {"name": "file-lock2", "title": "File lock2", "categories": ["Files and folders"], "tags": ["lock", "private", "secure"]}, {"name": "file-medical-fill", "title": "File medical fill", "categories": ["Files and folders"], "tags": ["doc", "document", "medical", "hospital", "health"]}, {"name": "file-medical", "title": "File medical", "categories": ["Files and folders"], "tags": ["doc", "document", "medical", "hospital", "health"]}, {"name": "file-minus-fill", "title": "File minus fill", "categories": ["Files and folders"], "tags": ["doc", "document", "delete", "remove"]}, {"name": "file-minus", "title": "File minus", "categories": ["Files and folders"], "tags": ["doc", "document", "delete", "remove"]}, {"name": "file-music-fill", "title": "File music fill", "categories": ["Files and folders"], "tags": ["doc", "document", "music", "audio", "playlist", "songs"]}, {"name": "file-music", "title": "File music", "categories": ["Files and folders"], "tags": ["doc", "document", "music", "audio", "playlist", "songs"]}, {"name": "file-pdf-fill", "title": "File PDF fill", "categories": ["Files and folders"], "tags": ["doc", "document", "adobe", "acrobat"]}, {"name": "file-pdf", "title": "File PDF", "categories": ["Files and folders"], "tags": ["doc", "document", "adobe", "acrobat"]}, {"name": "file-person-fill", "title": "File person fill", "categories": ["Files and folders"], "tags": ["doc", "document", "personal", "cv", "resume", "about"]}, {"name": "file-person", "title": "File person", "categories": ["Files and folders"], "tags": ["doc", "document", "personal", "cv", "resume", "about"]}, {"name": "file-play-fill", "title": "File play fill", "categories": ["Files and folders"], "tags": ["video", "present"]}, {"name": "file-play", "title": "File play", "categories": ["Files and folders"], "tags": ["video", "present"]}, {"name": "file-plus-fill", "title": "File plus fill", "categories": ["Files and folders"], "tags": ["doc", "document", "add", "new"]}, {"name": "file-plus", "title": "File plus", "categories": ["Files and folders"], "tags": ["doc", "document", "add", "new"]}, {"name": "file-post-fill", "title": "File post fill", "categories": ["Files and folders"], "tags": ["doc", "document", "post"]}, {"name": "file-post", "title": "File post", "categories": ["Files and folders"], "tags": ["doc", "document", "post"]}, {"name": "file-ppt-fill", "title": "File ppt fill", "categories": ["Files and folders"], "tags": ["slides", "presentation", "powerpoint", "keynote"]}, {"name": "file-ppt", "title": "File ppt", "categories": ["Files and folders"], "tags": ["slides", "presentation", "powerpoint", "keynote"]}, {"name": "file-richtext-fill", "title": "File richtext fill", "categories": ["Files and folders"], "tags": ["doc", "document", "richtext"]}, {"name": "file-richtext", "title": "File rich text", "categories": ["Files and folders"], "tags": ["doc", "document", "richtext"]}, {"name": "file-ruled-fill", "title": "File ruled fill", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "file-ruled", "title": "File ruled", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "file-slides-fill", "title": "File slides fill", "categories": ["Files and folders"], "tags": ["presentation", "keynote", "powerpoint"]}, {"name": "file-slides", "title": "File slides", "categories": ["Files and folders"], "tags": ["presentation", "keynote", "powerpoint"]}, {"name": "file-spreadsheet-fill", "title": "File spreadsheet fill", "categories": ["Files and folders"], "tags": ["doc", "document", "excel", "table"]}, {"name": "file-spreadsheet", "title": "File spreadsheet", "categories": ["Files and folders"], "tags": ["doc", "document", "excel", "table"]}, {"name": "file-text-fill", "title": "File text fill", "categories": ["Files and folders"], "tags": ["doc", "document", "letter"]}, {"name": "file-text", "title": "File text", "categories": ["Files and folders"], "tags": ["doc", "document", "letter"]}, {"name": "file-word-fill", "title": "File word fill", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "file-word", "title": "File word", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "file-x-fill", "title": "File x fill", "categories": ["Files and folders"], "tags": ["document", "remove", "delete"]}, {"name": "file-x", "title": "File x", "categories": ["Files and folders"], "tags": ["document", "remove", "delete"]}, {"name": "file-zip-fill", "title": "File zip fill", "categories": ["Files and folders"], "tags": ["doc", "document", "zip", "archive", "compress"]}, {"name": "file-zip", "title": "File zip", "categories": ["Files and folders"], "tags": ["doc", "document", "zip", "archive", "compress"]}, {"name": "file", "title": "File", "categories": ["Files and folders"], "tags": ["doc", "document", "page"]}, {"name": "files-alt", "title": "Files alt", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "files", "title": "Files", "categories": ["Files and folders"], "tags": ["doc", "document"]}, {"name": "filetype-aac", "title": "Filetype aac", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-ai", "title": "Filetype ai", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-bmp", "title": "Filetype bmp", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-cs", "title": "Filetype cs", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code"]}, {"name": "filetype-css", "title": "Filetype css", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "stylesheet"]}, {"name": "filetype-csv", "title": "Filetype csv", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-doc", "title": "Filetype doc", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-docx", "title": "Filetype docx", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-exe", "title": "Filetype exe", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-gif", "title": "Filetype gif", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-heic", "title": "Filetype heic", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-html", "title": "Filetype html", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "html"]}, {"name": "filetype-java", "title": "Filetype java", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "java"]}, {"name": "filetype-jpg", "title": "Filetype jpg", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-js", "title": "Filetype js", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "javascript"]}, {"name": "filetype-json", "title": "Filetype json", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "javascript"]}, {"name": "filetype-jsx", "title": "Filetype jsx", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "javascript", "react"]}, {"name": "filetype-key", "title": "Filetype key", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "keynote"]}, {"name": "filetype-m4p", "title": "Filetype m4p", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-md", "title": "Filetype md", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "markdown"]}, {"name": "filetype-mdx", "title": "Filetype mdx", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-mov", "title": "Filetype mov", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-mp3", "title": "Filetype mp3", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-mp4", "title": "Filetype mp4", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-otf", "title": "Filetype otf", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "font", "type"]}, {"name": "filetype-pdf", "title": "Filetype pdf", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-php", "title": "Filetype php", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code"]}, {"name": "filetype-png", "title": "Filetype png", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-ppt", "title": "Filetype ppt", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "powerpoint"]}, {"name": "filetype-pptx", "title": "Filetype pptx", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "powerpoint"]}, {"name": "filetype-psd", "title": "Filetype psd", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-py", "title": "Filetype py", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "python"]}, {"name": "filetype-raw", "title": "Filetype raw", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-rb", "title": "Filetype rb", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "ruby"]}, {"name": "filetype-sass", "title": "Filetype sass", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "css", "stylesheet"]}, {"name": "filetype-scss", "title": "Filetype scss", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "css", "stylesheet"]}, {"name": "filetype-sh", "title": "Filetype sh", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "shell"]}, {"name": "filetype-sql", "title": "Filetype SQL", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "mysql"]}, {"name": "filetype-svg", "title": "Filetype svg", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code"]}, {"name": "filetype-tiff", "title": "Filetype tiff", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-tsx", "title": "Filetype tsx", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "typescript"]}, {"name": "filetype-ttf", "title": "Filetype ttf", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "font", "type"]}, {"name": "filetype-txt", "title": "Filetype txt", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-wav", "title": "Filetype wav", "categories": ["Files and folders"], "tags": ["file", "file type", "extension"]}, {"name": "filetype-woff", "title": "Filetype woff", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "font", "type"]}, {"name": "filetype-xls", "title": "Filetype xls", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "spreadsheet"]}, {"name": "filetype-xlsx", "title": "Filetype xlsx", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "spreadsheet"]}, {"name": "filetype-xml", "title": "Filetype xml", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code"]}, {"name": "filetype-yml", "title": "Filetype yml", "categories": ["Files and folders"], "tags": ["file", "file type", "extension", "code", "yaml"]}, {"name": "film", "title": "Film", "categories": ["Media"], "tags": ["video", "movie"]}, {"name": "filter-circle-fill", "title": "Filter circle fill", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "filter-circle", "title": "Filter circle", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "filter-left", "title": "Filter left", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "filter-right", "title": "Filter right", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "filter-square-fill", "title": "Filter square fill", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "filter-square", "title": "Filter square", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "filter", "title": "Filter", "categories": ["UI and keyboard"], "tags": ["sort"]}, {"name": "fingerprint", "title": "Fingerprint", "categories": ["Security"], "tags": ["touch ID", "secure", "biometric"]}, {"name": "fire", "title": "Fire", "categories": ["Real world"], "tags": ["burning", "smoke"]}, {"name": "flag-fill", "title": "Flag fill", "categories": ["Communications"], "tags": ["report"]}, {"name": "flag", "title": "Flag", "categories": ["Communications"], "tags": ["report"]}, {"name": "floppy-fill", "title": "Floppy fill", "categories": ["Devices"], "tags": ["save", "storage", "disk"]}, {"name": "floppy", "title": "Floppy", "categories": ["Devices"], "tags": ["save", "storage", "disk"]}, {"name": "floppy2-fill", "title": "Floppy2 fill", "categories": ["Devices"], "tags": ["save", "storage", "disk"]}, {"name": "floppy2", "title": "Floppy2", "categories": ["Devices"], "tags": ["save", "storage", "disk"]}, {"name": "flower1", "title": "Flower1", "categories": ["Real world"], "tags": ["plant", "bloom", "flower"]}, {"name": "flower2", "title": "Flower2", "categories": ["Real world"], "tags": ["plant", "bloom", "flower"]}, {"name": "flower3", "title": "Flower3", "categories": ["Real world"], "tags": ["plant", "bloom", "flower"]}, {"name": "folder-check", "title": "Folder check", "categories": ["Files and folders"], "tags": ["directory", "check", "verified"]}, {"name": "folder-fill", "title": "Folder fill", "categories": ["Files and folders"], "tags": ["directory"]}, {"name": "folder-minus", "title": "Folder minus", "categories": ["Files and folders"], "tags": ["directory", "delete", "remove"]}, {"name": "folder-plus", "title": "Folder plus", "categories": ["Files and folders"], "tags": ["directory", "delete", "add", "new"]}, {"name": "folder-symlink-fill", "title": "Folder symlink fill", "categories": ["Files and folders"], "tags": ["directory", "symbolic-link"]}, {"name": "folder-symlink", "title": "Folder symlink", "categories": ["Files and folders"], "tags": ["directory", "symbolic-link"]}, {"name": "folder-x", "title": "Folder x", "categories": ["Files and folders"], "tags": ["directory", "remove", "delete"]}, {"name": "folder", "title": "Folder", "categories": ["Files and folders"], "tags": ["directory"]}, {"name": "folder2-open", "title": "Folder2 open", "categories": ["Files and folders"], "tags": ["directory"]}, {"name": "folder2", "title": "Folder2", "categories": ["Files and folders"], "tags": ["directory"]}, {"name": "fonts", "title": "Fonts", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "forward-fill", "title": "Forward fill", "categories": ["Communications"], "tags": ["mail", "email"]}, {"name": "forward", "title": "Forward", "categories": ["Communications"], "tags": ["mail", "email"]}, {"name": "front", "title": "Front", "categories": ["Graphics"], "tags": ["forward", "layer"]}, {"name": "fuel-pump-diesel-fill", "title": "Fuel pump diesel fill", "categories": ["Transportation"], "tags": ["gas", "petrol", "fuel", "gas station"]}, {"name": "fuel-pump-diesel", "title": "Fuel pump diesel", "categories": ["Transportation"], "tags": ["gas", "petrol", "fuel", "gas station"]}, {"name": "fuel-pump-fill", "title": "Fuel pump fill", "categories": ["Transportation"], "tags": ["gas", "petrol", "fuel", "gas station"]}, {"name": "fuel-pump", "title": "Fuel pump", "categories": ["Transportation"], "tags": ["gas", "petrol", "fuel", "gas station"]}, {"name": "fullscreen-exit", "title": "Fullscreen exit", "categories": ["UI and keyboard"], "tags": ["window", "minimize"]}, {"name": "fullscreen", "title": "Fullscreen", "categories": ["UI and keyboard"], "tags": ["window", "maximize"]}, {"name": "funnel-fill", "title": "Funnel fill", "categories": ["Real world"], "tags": ["sort", "filter"]}, {"name": "funnel", "title": "Funnel", "categories": ["Real world"], "tags": ["sort", "filter"]}, {"name": "gear-fill", "title": "Gear fill", "categories": ["Tools"], "tags": ["tool", "settings", "preferences"]}, {"name": "gear-wide-connected", "title": "Gear wide connected", "categories": ["Tools"], "tags": ["tool", "settings", "preferences"]}, {"name": "gear-wide", "title": "Gear wide", "categories": ["Tools"], "tags": ["tool", "settings", "preferences"]}, {"name": "gear", "title": "Gear", "categories": ["Tools"], "tags": ["tool", "settings", "preferences"]}, {"name": "gem", "title": "Gem", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "diamond"]}, {"name": "gender-ambiguous", "title": "Gender ambiguous", "categories": ["People"], "tags": ["gender", "identity"]}, {"name": "gender-female", "title": "Gender female", "categories": ["People"], "tags": ["gender", "identity"]}, {"name": "gender-male", "title": "Gender male", "categories": ["People"], "tags": ["gender", "identity"]}, {"name": "gender-neuter", "title": "Gender neuter", "categories": ["People"], "tags": ["gender", "identity"]}, {"name": "gender-trans", "title": "Gender trans", "categories": ["People"], "tags": ["gender", "identity"]}, {"name": "geo-alt-fill", "title": "Geo alt fill", "categories": ["Geo"], "tags": ["geography", "map", "pin", "location"]}, {"name": "geo-alt", "title": "Geo alt", "categories": ["Geo"], "tags": ["geography", "map", "pin", "location"]}, {"name": "geo-fill", "title": "Geo fill", "categories": ["Geo"], "tags": ["geography", "map", "pin", "location"]}, {"name": "geo", "title": "Geo", "categories": ["Geo"], "tags": ["geography", "map", "pin", "location"]}, {"name": "gift-fill", "title": "Gift fill", "categories": ["Real world"], "tags": ["present", "gift"]}, {"name": "gift", "title": "Gift", "categories": ["Real world"], "tags": ["present", "gift"]}, {"name": "git", "title": "Git", "categories": ["Brand"], "tags": ["version control", "vcs", "github", "version"]}, {"name": "github", "title": "GitHub", "categories": ["Brand"], "tags": ["social", "microsoft", "vcs", "git"]}, {"name": "gitlab", "title": "Gitlab", "categories": ["Brand"], "tags": ["social", "vcs", "git"]}, {"name": "globe-americas", "title": "Globe Americas", "categories": ["Geo"], "tags": ["geography", "earth", "world", "map"]}, {"name": "globe-asia-australia", "title": "Globe Asia Australia", "categories": ["Geo"], "tags": ["geography", "earth", "world", "map"]}, {"name": "globe-central-south-asia", "title": "Globe Central South Asia", "categories": ["Geo"], "tags": ["geography", "earth", "world", "map"]}, {"name": "globe-europe-africa", "title": "Globe Europe Africa", "categories": ["Geo"], "tags": ["geography", "earth", "world", "map"]}, {"name": "globe", "title": "Globe", "categories": ["Communications"], "tags": ["world", "translate", "global", "international"]}, {"name": "globe2", "title": "Globe2", "categories": ["Communications"], "tags": ["world", "translate", "global", "international"]}, {"name": "google-play", "title": "Google Play", "categories": ["Brand"], "tags": ["app store", "apps", "store", "android"]}, {"name": "google", "title": "Google", "categories": ["Brand"], "tags": ["social", "search"]}, {"name": "gpu-card", "title": "GPU card", "categories": ["Devices"], "tags": ["video", "output", "graphics"]}, {"name": "graph-down-arrow", "title": "Graph down arrow", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "graph-down", "title": "Graph down", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "graph-up-arrow", "title": "Graph up arrow", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "graph-up", "title": "Graph up", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "grid-1x2-fill", "title": "Grid 1x2 fill", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "grid-1x2", "title": "Grid 1x2", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "grid-3x2-gap-fill", "title": "Grid 3x2 gap fill", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "grid-3x2-gap", "title": "Grid 3x2 gap", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "grid-3x2", "title": "Grid 3x2", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "grid-3x3-gap-fill", "title": "Grid 3x3 gap fill", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "grid-3x3-gap", "title": "Grid 3x3 gap", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "grid-3x3", "title": "Grid 3x3", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "grid-fill", "title": "Grid fill", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "grid", "title": "Grid", "categories": ["Layout"], "tags": ["grid", "layout"]}, {"name": "grip-horizontal", "title": "Grip horizontal", "categories": ["UI and keyboard"], "tags": ["drag", "grab"]}, {"name": "grip-vertical", "title": "Grip vertical", "categories": ["UI and keyboard"], "tags": ["drag", "grab"]}, {"name": "h-circle-fill", "title": "H circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["hospital", "helicopter"]}, {"name": "h-circle", "title": "H circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["hospital", "helicopter"]}, {"name": "h-square-fill", "title": "H square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["hospital", "helicopter"]}, {"name": "h-square", "title": "H square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["hospital", "helicopter"]}, {"name": "hammer", "title": "Hammer", "categories": ["Tools"], "tags": ["tool"]}, {"name": "hand-index-fill", "title": "Hand index fill", "categories": ["Hands"], "tags": ["hand", "pointer", "cursor"]}, {"name": "hand-index-thumb-fill", "title": "Hand index thumb fill", "categories": ["Hands"], "tags": ["hand", "pointer", "cursor"]}, {"name": "hand-index-thumb", "title": "Hand index thumb", "categories": ["Hands"], "tags": ["hand", "pointer", "cursor"]}, {"name": "hand-index", "title": "Hand index", "categories": ["Hands"], "tags": ["hand", "pointer", "cursor"]}, {"name": "hand-thumbs-down-fill", "title": "Hand thumbs down fill", "categories": ["Hands"], "tags": ["hand", "pointer", "thumbs-down", "-1"]}, {"name": "hand-thumbs-down", "title": "Hand thumbs down", "categories": ["Hands"], "tags": ["hand", "pointer", "thumbs-down", "-1"]}, {"name": "hand-thumbs-up-fill", "title": "Hand thumbs up fill", "categories": ["Hands"], "tags": ["hand", "pointer", "thumbs-up", "+1"]}, {"name": "hand-thumbs-up", "title": "Hand thumbs up", "categories": ["Hands"], "tags": ["hand", "pointer", "thumbs-up", "+1"]}, {"name": "handbag-fill", "title": "Handbag fill", "categories": ["Real world"], "tags": ["purse", "tote"]}, {"name": "handbag", "title": "Handbag", "categories": ["Real world"], "tags": ["purse", "tote"]}, {"name": "hash", "title": "Hash", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "hdd-fill", "title": "Hdd fill", "categories": ["Devices"], "tags": ["hard drive", "hard disk", "ssd", "drive", "server"]}, {"name": "hdd-network-fill", "title": "Hdd network fill", "categories": ["Devices"], "tags": ["hard drive", "hard disk", "ssd", "drive", "server"]}, {"name": "hdd-network", "title": "Hdd network", "categories": ["Devices"], "tags": ["hard drive", "hard disk", "ssd", "drive", "server"]}, {"name": "hdd-rack-fill", "title": "Hdd rack fill", "categories": ["Devices"], "tags": ["hard drive", "hard disk", "ssd", "drive", "server"]}, {"name": "hdd-rack", "title": "Hdd rack", "categories": ["Devices"], "tags": ["hard drive", "hard disk", "ssd", "drive", "server"]}, {"name": "hdd-stack-fill", "title": "Hdd stack fill", "categories": ["Devices"], "tags": ["hard drive", "hard disk", "ssd", "drive", "server"]}, {"name": "hdd-stack", "title": "Hdd stack", "categories": ["Devices"], "tags": ["hard drive", "hard disk", "ssd", "drive", "server"]}, {"name": "hdd", "title": "Hdd", "categories": ["Devices"], "tags": ["hard drive", "hard disk", "ssd", "drive", "server"]}, {"name": "hdmi-fill", "title": "HDMI fill", "categories": ["Devices"], "tags": ["video", "input"]}, {"name": "hdmi", "title": "HDMI", "categories": ["Devices"], "tags": ["video", "input"]}, {"name": "headphones", "title": "Headphones", "categories": ["Devices"], "tags": ["headphones"]}, {"name": "headset-vr", "title": "Headset VR", "categories": ["Devices"], "tags": ["virual reality", "oculus", "hololens"]}, {"name": "headset", "title": "Headset", "categories": ["Devices"], "tags": ["headphones", "microphone"]}, {"name": "heart-arrow", "title": "Heart arrow", "categories": ["Love"], "tags": ["romance", "cupid", "valentine"]}, {"name": "heart-fill", "title": "Heart fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["love", "favorite"]}, {"name": "heart-half", "title": "Heart half", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["love", "favorite"]}, {"name": "heart-pulse-fill", "title": "Heart pulse fill", "categories": ["Medical"], "tags": ["heartbeat"]}, {"name": "heart-pulse", "title": "Heart pulse", "categories": ["Medical"], "tags": ["heartbeat"]}, {"name": "heart", "title": "Heart", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["love", "favorite"]}, {"name": "heartbreak-fill", "title": "Heartbreak fill", "categories": ["<PERSON><PERSON><PERSON>", "Love"], "tags": ["love", "valentine", "romance"]}, {"name": "heartbreak", "title": "Heartbreak", "categories": ["<PERSON><PERSON><PERSON>", "Love"], "tags": ["love", "valentine", "romance"]}, {"name": "hearts", "title": "Hearts", "categories": ["Love"], "tags": ["romance", "valentine"]}, {"name": "heptagon-fill", "title": "Heptagon fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "heptagon-half", "title": "Heptagon half", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "heptagon", "title": "Heptagon", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "hexagon-fill", "title": "Hexagon fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "hexagon-half", "title": "Hexagon half", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "hexagon", "title": "Hexagon", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "highlighter", "title": "Highlighter", "categories": ["Tools"], "tags": ["text", "study", "highlight", "marker"]}, {"name": "highlights", "title": "Highlights", "categories": ["Graphics"], "tags": ["photo", "editing", "image", "picture"]}, {"name": "hospital-fill", "title": "Hospital fill", "categories": ["Medical"], "tags": ["health", "emergency room"]}, {"name": "hospital", "title": "Hospital", "categories": ["Medical"], "tags": ["health", "emergency room"]}, {"name": "hourglass-bottom", "title": "Hourglass bottom", "categories": ["Real world"], "tags": ["time", "history", "wait", "sand", "clock"]}, {"name": "hourglass-split", "title": "Hourglass split", "categories": ["Real world"], "tags": ["time", "history", "wait", "sand", "clock"]}, {"name": "hourglass-top", "title": "Hourglass top", "categories": ["Real world"], "tags": ["time", "history", "wait", "sand", "clock"]}, {"name": "hourglass", "title": "Hourglass", "categories": ["Real world"], "tags": ["time", "history", "wait", "sand", "clock"]}, {"name": "house-add-fill", "title": "House add fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-add", "title": "House add", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-check-fill", "title": "House check fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-check", "title": "House check", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-dash-fill", "title": "House dash fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-dash", "title": "House dash", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-door-fill", "title": "House door fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-door", "title": "House door", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-down-fill", "title": "House down fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-down", "title": "House down", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-exclamation-fill", "title": "House exclamation fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-exclamation", "title": "House exclamation", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-fill", "title": "House fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-gear-fill", "title": "House gear fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-gear", "title": "House gear", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-heart-fill", "title": "House heart fill", "categories": ["Real world"], "tags": ["home", "love", "romance"]}, {"name": "house-heart", "title": "House heart", "categories": ["Real world"], "tags": ["home", "love", "romance"]}, {"name": "house-lock-fill", "title": "House lock fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-lock", "title": "House lock", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-slash-fill", "title": "House slash fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-slash", "title": "House slash", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-up-fill", "title": "House up fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-up", "title": "House up", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-x-fill", "title": "House x fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "house-x", "title": "House x", "categories": ["Real world"], "tags": ["home"]}, {"name": "house", "title": "House", "categories": ["Real world"], "tags": ["home"]}, {"name": "houses-fill", "title": "Houses fill", "categories": ["Real world"], "tags": ["home"]}, {"name": "houses", "title": "Houses", "categories": ["Real world"], "tags": ["home"]}, {"name": "hr", "title": "HR", "categories": ["Typography"], "tags": ["divider", "horizonal-rule"]}, {"name": "hurricane", "title": "Hurricane", "categories": ["Weather"], "tags": ["storm"]}, {"name": "hypnotize", "title": "Hypnotize", "categories": ["Real World"], "tags": ["hypnosis", "sleepy"]}, {"name": "image-alt", "title": "Image alt", "categories": ["Files and folders"], "tags": ["picture", "photo"]}, {"name": "image-fill", "title": "Image fill", "categories": ["Files and folders"], "tags": ["picture", "photo"]}, {"name": "image", "title": "Image", "categories": ["Files and folders"], "tags": ["picture", "photo"]}, {"name": "images", "title": "Images", "categories": ["Files and folders"], "tags": ["picture", "photo"]}, {"name": "inbox-fill", "title": "Inbox fill", "categories": ["Communications"], "tags": ["mail", "email", "letter tray"]}, {"name": "inbox", "title": "Inbox", "categories": ["Communications"], "tags": ["mail", "email", "letter tray"]}, {"name": "inboxes-fill", "title": "Inboxes fill", "categories": ["Communications"], "tags": ["mail", "email", "letter tray"]}, {"name": "inboxes", "title": "Inboxes fill", "categories": ["Communications"], "tags": ["mail", "email", "letter tray"]}, {"name": "incognito", "title": "Incognito", "categories": ["Miscellaneous"], "tags": ["private", "investigator", "secret"]}, {"name": "indent", "title": "Indent", "categories": ["UI and Keyboard"], "tags": ["tab", "indent"]}, {"name": "infinity", "title": "Infinity", "categories": ["Typography"], "tags": ["math", "infinite"]}, {"name": "info-circle-fill", "title": "Info fill", "categories": ["Alerts, warnings, and signs"], "tags": ["information", "help"]}, {"name": "info-circle", "title": "Info circle", "categories": ["Alerts, warnings, and signs"], "tags": ["information", "help"]}, {"name": "info-lg", "title": "Info lg", "categories": ["Alerts, warnings, and signs"], "tags": ["information", "help"]}, {"name": "info-square-fill", "title": "Info square fill", "categories": ["Alerts, warnings, and signs"], "tags": ["information", "help"]}, {"name": "info-square", "title": "Info square", "categories": ["Alerts, warnings, and signs"], "tags": ["information", "help"]}, {"name": "info", "title": "Info", "categories": ["Alerts, warnings, and signs"], "tags": ["information", "help"]}, {"name": "input-cursor-text", "title": "Input cursor text", "categories": ["Graphics"], "tags": ["textarea", "form", "input"]}, {"name": "input-cursor", "title": "Input cursor", "categories": ["Graphics"], "tags": ["textarea", "form", "input"]}, {"name": "instagram", "title": "Instagram", "categories": ["Brand"], "tags": ["social", "chat"]}, {"name": "intersect", "title": "Intersect", "categories": ["Graphics"], "tags": ["graphics", "vector", "merge", "layers"]}, {"name": "journal-album", "title": "Journal album", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-arrow-down", "title": "Journal arrow down", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-arrow-up", "title": "Journal arrow up", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-bookmark-fill", "title": "Journal bookmark fill", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-bookmark", "title": "Journal bookmark", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-check", "title": "Journal check", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-code", "title": "Journal code", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-medical", "title": "Journal medical", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-minus", "title": "Journal minus", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-plus", "title": "Journal plus", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-richtext", "title": "Journal richtext", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-text", "title": "Journal text", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journal-x", "title": "Journal x", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook", "remove", "delete"]}, {"name": "journal", "title": "Journal", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "journals", "title": "Journals", "categories": ["Files and folders"], "tags": ["file", "folder", "journal", "notebook"]}, {"name": "joystick", "title": "Joystick", "categories": ["Entertainment"], "tags": ["gaming", "game", "video games"]}, {"name": "justify-left", "title": "Justify left", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment"]}, {"name": "justify-right", "title": "Justify right", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment"]}, {"name": "justify", "title": "Justify", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment"]}, {"name": "kanban-fill", "title": "Kanban fill", "categories": ["Miscellaneous"], "tags": ["board", "project-management"]}, {"name": "kanban", "title": "Ka<PERSON><PERSON>", "categories": ["Miscellaneous"], "tags": ["board", "project-management"]}, {"name": "key-fill", "title": "Key fill", "categories": ["Real world"], "tags": ["lock", "secure"]}, {"name": "key", "title": "Key", "categories": ["Real world"], "tags": ["lock", "secure"]}, {"name": "keyboard-fill", "title": "Keyboard fill", "categories": ["Devices"], "tags": ["keyboard", "keys", "typing"]}, {"name": "keyboard", "title": "Keyboard", "categories": ["Devices"], "tags": ["keyboard", "keys", "typing"]}, {"name": "ladder", "title": "Ladder", "categories": ["Real world"], "tags": ["climb", "ladder"]}, {"name": "lamp-fill", "title": "Lamp fill", "categories": ["Real world"], "tags": ["light", "lamp"]}, {"name": "lamp", "title": "<PERSON><PERSON>", "categories": ["Real world"], "tags": ["light", "lamp"]}, {"name": "laptop-fill", "title": "Laptop fill", "categories": ["Devices"], "tags": ["computer"]}, {"name": "laptop", "title": "Laptop", "categories": ["Devices"], "tags": ["computer"]}, {"name": "layer-backward", "title": "Layer backward", "categories": ["Graphics"], "tags": ["arrange", "layers", "back"]}, {"name": "layer-forward", "title": "Layer forward", "categories": ["Graphics"], "tags": ["arrange", "layers", "front"]}, {"name": "layers-fill", "title": "Layers fill", "categories": ["Graphics"], "tags": ["perspective", "stacked"]}, {"name": "layers-half", "title": "Layers half", "categories": ["Graphics"], "tags": ["perspective", "stacked"]}, {"name": "layers", "title": "Layers", "categories": ["Graphics"], "tags": ["perspective", "stacked"]}, {"name": "layout-sidebar-inset-reverse", "title": "Layout sidebar inset reverse", "categories": ["Layout"], "tags": ["layout", "columns"]}, {"name": "layout-sidebar-inset", "title": "Layout sidebar nested", "categories": ["Layout"], "tags": ["layout", "columns"]}, {"name": "layout-sidebar-reverse", "title": "Layout sidebar reverse", "categories": ["Layout"], "tags": ["grid", "layout", "sidebar"]}, {"name": "layout-sidebar", "title": "Layout sidebar", "categories": ["Layout"], "tags": ["grid", "layout", "sidebar"]}, {"name": "layout-split", "title": "Layout split", "categories": ["Layout"], "tags": ["grid", "layout", "sidebar"]}, {"name": "layout-text-sidebar-reverse", "title": "Layout text sidebar reverse", "categories": ["Layout"], "tags": ["layout", "columns"]}, {"name": "layout-text-sidebar", "title": "Layout text sidebar", "categories": ["Layout"], "tags": ["layout", "columns"]}, {"name": "layout-text-window-reverse", "title": "Layout text window reverse", "categories": ["Layout"], "tags": ["layout", "columns"]}, {"name": "layout-text-window", "title": "Layout text window", "categories": ["Layout"], "tags": ["layout", "columns"]}, {"name": "layout-three-columns", "title": "Layout three columns", "categories": ["Layout"], "tags": ["layout", "columns"]}, {"name": "layout-wtf", "title": "Layout WTF", "categories": ["Layout"], "tags": ["layout", "broken"]}, {"name": "life-preserver", "title": "Life preserver", "categories": ["Real world"], "tags": ["lifesaver", "water"]}, {"name": "lightbulb-fill", "title": "Lightbulb fill", "categories": ["Real world"], "tags": ["lights", "lamp"]}, {"name": "lightbulb-off-fill", "title": "Lightbulb off fill", "categories": ["Real world"], "tags": ["lights", "lamp"]}, {"name": "lightbulb-off", "title": "Lightbulb off", "categories": ["Real world"], "tags": ["lights", "lamp"]}, {"name": "lightbulb", "title": "Lightbulb", "categories": ["Real world"], "tags": ["lights", "lamp"]}, {"name": "lightning-charge-fill", "title": "Lightning charge fill", "categories": ["Miscellaneous"], "tags": ["weather", "storm", "thunder", "bolt"]}, {"name": "lightning-charge", "title": "Lightning charge", "categories": ["Miscellaneous"], "tags": ["weather", "storm", "thunder", "bolt"]}, {"name": "lightning-fill", "title": "Lightning fill", "categories": ["Weather"], "tags": ["storm", "thunder", "bolt"]}, {"name": "lightning", "title": "Lightning", "categories": ["Weather"], "tags": ["storm", "thunder", "bolt"]}, {"name": "line", "title": "Line", "categories": ["Brand"], "tags": ["social"]}, {"name": "link-45deg", "title": "Link 45deg", "categories": ["UI and keyboard"], "tags": ["anchor", "hyperlink", "href"]}, {"name": "link", "title": "Link", "categories": ["UI and keyboard"], "tags": ["anchor", "hyperlink", "href"]}, {"name": "linkedin", "title": "Linkedin", "categories": ["Brand"], "tags": ["social", "microsoft"]}, {"name": "list-check", "title": "List checked", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment", "checklist", "done"]}, {"name": "list-columns-reverse", "title": "List columns reverse", "categories": ["Typography"], "tags": ["text", "type", "alignment"]}, {"name": "list-columns", "title": "List columns", "categories": ["Typography"], "tags": ["text", "type", "alignment"]}, {"name": "list-nested", "title": "List nested", "categories": ["Typography"], "tags": ["text", "type", "alignment", "children"]}, {"name": "list-ol", "title": "List OL", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment", "ordered-list", "numbered-list", "numbered"]}, {"name": "list-stars", "title": "List stars", "categories": ["Typography"], "tags": ["text", "type", "starred"]}, {"name": "list-task", "title": "List task", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment", "todos", "task"]}, {"name": "list-ul", "title": "List UL", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment", "unordered-list"]}, {"name": "list", "title": "List", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment", "menu", "hamburger"]}, {"name": "lock-fill", "title": "Lock fill", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "lock", "title": "Lock", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "luggage-fill", "title": "Luggage fill", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "luggage", "title": "Luggage", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on", "student", "education"]}, {"name": "lungs-fill", "title": "Lungs fill", "categories": ["Medical"], "tags": ["medicine", "health", "breathing"]}, {"name": "lungs", "title": "<PERSON><PERSON><PERSON>", "categories": ["Medical"], "tags": ["medicine", "health", "breathing"]}, {"name": "magic", "title": "Magic", "categories": ["Real World"], "tags": ["magic stick"]}, {"name": "magnet-fill", "title": "Magnet fill", "categories": ["Real World"], "tags": ["magnetic"]}, {"name": "magnet", "title": "<PERSON><PERSON><PERSON>", "categories": ["Real World"], "tags": ["magnetic"]}, {"name": "mailbox-flag", "title": "Mailbox flag", "categories": ["Real world"], "tags": ["post", "postal", "postbox", "letterbox"]}, {"name": "mailbox", "title": "Mailbox", "categories": ["Real world"], "tags": ["post", "postal", "postbox", "letterbox"]}, {"name": "mailbox2-flag", "title": "Mailbox2 flag", "categories": ["Real world"], "tags": ["post", "postal", "postbox", "letterbox"]}, {"name": "mailbox2", "title": "Mailbox2", "categories": ["Real world"], "tags": ["post", "postal", "postbox", "letterbox"]}, {"name": "map-fill", "title": "Map fill", "categories": ["Geo"], "tags": ["geography", "directions", "location"]}, {"name": "map", "title": "Map", "categories": ["Geo"], "tags": ["geography", "directions", "location"]}, {"name": "markdown-fill", "title": "Markdown fill", "categories": ["Badges"], "tags": ["markdown", "md", "content", "writing"]}, {"name": "markdown", "title": "<PERSON><PERSON>", "categories": ["Badges"], "tags": ["markdown", "md", "content", "writing"]}, {"name": "marker-tip", "title": "Marker tip", "categories": ["Tools"], "tags": ["text", "study", "highlight"]}, {"name": "mask", "title": "Mask", "categories": ["Graphics"], "tags": ["mask"]}, {"name": "mastodon", "title": "Mastodon", "categories": ["Brand"], "tags": ["social"]}, {"name": "medium", "title": "Medium", "categories": ["Brand"], "tags": ["social"]}, {"name": "megaphone-fill", "title": "Megaphone fill", "categories": ["Real world"], "tags": ["loudspeaker", "announcement"]}, {"name": "megaphone", "title": "Megaphone", "categories": ["Real world"], "tags": ["loudspeaker", "announcement"]}, {"name": "memory", "title": "Memory", "categories": ["Devices"], "tags": ["ram"]}, {"name": "menu-app-fill", "title": "Menu app fill", "categories": ["Controls"], "tags": ["dropdown", "menu", "context", "app", "ui"]}, {"name": "menu-app", "title": "Menu app", "categories": ["Controls"], "tags": ["dropdown", "menu", "context", "app", "ui"]}, {"name": "menu-button-fill", "title": "Menu button fill", "categories": ["Controls"], "tags": ["dropdown", "menu", "context", "app", "ui"]}, {"name": "menu-button-wide-fill", "title": "Menu button wide fill", "categories": ["Controls"], "tags": ["dropdown", "menu", "context", "app", "ui"]}, {"name": "menu-button-wide", "title": "Menu button wide", "categories": ["Controls"], "tags": ["dropdown", "menu", "context", "app", "ui"]}, {"name": "menu-button", "title": "Menu button", "categories": ["Controls"], "tags": ["dropdown", "menu", "context", "app", "ui"]}, {"name": "menu-down", "title": "<PERSON>u down", "categories": ["Controls"], "tags": ["dropdown", "menu", "context", "app", "ui"]}, {"name": "menu-up", "title": "Menu up", "categories": ["Controls"], "tags": ["dropdown", "menu", "context", "app", "ui"]}, {"name": "messenger", "title": "<PERSON>", "categories": ["Brand"], "tags": ["social", "facebook", "chat"]}, {"name": "meta", "title": "Meta", "categories": ["Brand"], "tags": ["facebook", "fb"]}, {"name": "mic-fill", "title": "Mic fill", "categories": ["Media"], "tags": ["audio", "video", "av", "sound", "input", "microphone", "podcast"]}, {"name": "mic-mute-fill", "title": "Mic mute fill", "categories": ["Media"], "tags": ["audio", "video", "av", "sound", "input", "microphone"]}, {"name": "mic-mute", "title": "Mic mute", "categories": ["Media"], "tags": ["audio", "video", "av", "sound", "input", "microphone"]}, {"name": "mic", "title": "Mic", "categories": ["Media"], "tags": ["audio", "video", "av", "sound", "input", "microphone", "podcast"]}, {"name": "microsoft-teams", "title": "Microsoft Teams", "categories": ["Brand"], "tags": ["social", "messaging"]}, {"name": "microsoft", "title": "Microsoft", "categories": ["Brand"], "tags": ["msft", "windows"]}, {"name": "minecart-loaded", "title": "Minecart loaded", "categories": ["Real world"], "tags": ["mining", "cart"]}, {"name": "minecart", "title": "Minecart", "categories": ["Real world"], "tags": ["mining", "cart"]}, {"name": "modem-fill", "title": "Modem fill", "categories": ["Devices"], "tags": ["internet", "cable"]}, {"name": "modem", "title": "Modem", "categories": ["Devices"], "tags": ["internet", "cable"]}, {"name": "moisture", "title": "Moisture", "categories": ["Weather"], "tags": ["water", "droplets"]}, {"name": "moon-fill", "title": "Moon fill", "categories": ["Weather"], "tags": ["night", "sky"]}, {"name": "moon-stars-fill", "title": "Moon stars fill", "categories": ["Weather"], "tags": ["night", "sky"]}, {"name": "moon-stars", "title": "Moon stars", "categories": ["Weather"], "tags": ["night", "sky"]}, {"name": "moon", "title": "Moon", "categories": ["Real world"], "tags": ["lunar", "weather", "night"]}, {"name": "mortarboard-fill", "title": "Mortorboard fill", "categories": ["Real World"], "tags": ["graduation", "cap"]}, {"name": "mortarboard", "title": "Mortorboard", "categories": ["Real World"], "tags": ["graduation", "cap"]}, {"name": "motherboard-fill", "title": "Motherboard fill", "categories": ["Devices"], "tags": ["mobo", "computer", "hardware"]}, {"name": "motherboard", "title": "Motherboard", "categories": ["Devices"], "tags": ["mobo", "computer", "hardware"]}, {"name": "mouse-fill", "title": "Mouse fill", "categories": ["Devices"], "tags": ["mice", "input"]}, {"name": "mouse", "title": "Mouse", "categories": ["Devices"], "tags": ["mice", "input"]}, {"name": "mouse2-fill", "title": "Mouse2 fill", "categories": ["Devices"], "tags": ["mice", "input"]}, {"name": "mouse2", "title": "Mouse2", "categories": ["Devices"], "tags": ["mice", "input"]}, {"name": "mouse3-fill", "title": "Mouse3 fill", "categories": ["Devices"], "tags": ["mice", "input"]}, {"name": "mouse3", "title": "Mouse3", "categories": ["Devices"], "tags": ["mice", "input"]}, {"name": "music-note-beamed", "title": "Music note beamed", "categories": ["Media"], "tags": ["music", "notes", "audio", "sound"]}, {"name": "music-note-list", "title": "Music note list", "categories": ["Media"], "tags": ["music", "notes", "audio", "sound", "playlist", "library"]}, {"name": "music-note", "title": "Music note", "categories": ["Media"], "tags": ["music", "notes", "audio", "sound"]}, {"name": "music-player-fill", "title": "Music player fill", "categories": ["Devices"], "tags": ["ipod", "mp3"]}, {"name": "music-player", "title": "Music player", "categories": ["Devices"], "tags": ["ipod", "mp3"]}, {"name": "newspaper", "title": "Newspaper", "categories": ["Real world"], "tags": ["news", "paper"]}, {"name": "nintendo-switch", "title": "Nintendo Switch", "categories": ["Brand"], "tags": ["gaming"]}, {"name": "node-minus-fill", "title": "Node minus fill", "categories": ["Graphics"], "tags": ["add", "new", "node", "diagram"]}, {"name": "node-minus", "title": "Node minus", "categories": ["Graphics"], "tags": ["add", "new", "node", "diagram"]}, {"name": "node-plus-fill", "title": "Node plus fill", "categories": ["Graphics"], "tags": ["add", "new", "node", "diagram"]}, {"name": "node-plus", "title": "Node plus", "categories": ["Graphics"], "tags": ["add", "new", "node", "diagram"]}, {"name": "noise-reduction", "title": "Noise reduction", "categories": ["Graphics"], "tags": ["photo", "editing", "image", "picture"]}, {"name": "nut-fill", "title": "Nut fill", "categories": ["Tools"], "tags": ["nut", "bolt", "hexagon", "tools"]}, {"name": "nut", "title": "Nut", "categories": ["Tools"], "tags": ["nut", "bolt", "hexagon", "tools"]}, {"name": "nvidia", "title": "Nvidia", "categories": ["Brand"], "tags": ["gpu"]}, {"name": "nvme-fill", "title": "Nvme fill", "categories": ["Devices"], "tags": ["ssd", "hard drive", "hard disk", "storage"]}, {"name": "nvme", "title": "Nvme", "categories": ["Devices"], "tags": ["ssd", "hard drive", "hard disk", "storage"]}, {"name": "octagon-fill", "title": "Octagon fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "octagon-half", "title": "Octagon half", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "octagon", "title": "Octagon", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "opencollective", "title": "Opencollective", "categories": ["Brand"], "tags": ["social"]}, {"name": "optical-audio-fill", "title": "Optical audio fill", "categories": ["Devices"], "tags": ["port", "plug", "sound"]}, {"name": "optical-audio", "title": "Optical audio", "categories": ["Devices"], "tags": ["port", "plug", "sound"]}, {"name": "option", "title": "Option", "categories": ["UI and keyboard"], "tags": ["key", "mac"]}, {"name": "outlet", "title": "Outlet", "categories": ["Real world"], "tags": ["plug", "power"]}, {"name": "p-circle-fill", "title": "P circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["parking"]}, {"name": "p-circle", "title": "P circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["parking"]}, {"name": "p-square-fill", "title": "P square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["parking"]}, {"name": "p-square", "title": "P square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["parking"]}, {"name": "paint-bucket", "title": "Paint bucket", "categories": ["Graphics"], "tags": ["color", "paint", "fill", "palette"]}, {"name": "palette-fill", "title": "Palette fill", "categories": ["Graphics"], "tags": ["color", "paint", "fill", "palette"]}, {"name": "palette", "title": "Palette", "categories": ["Graphics"], "tags": ["color", "paint", "fill", "palette"]}, {"name": "palette2", "title": "Palette2", "categories": ["Graphics"], "tags": ["color", "paint", "fill", "palette"]}, {"name": "paperclip", "title": "Paperclip", "categories": ["Real world"], "tags": ["attachment"]}, {"name": "paragraph", "title": "Paragraph", "categories": ["Typography"], "tags": ["paragraph", "text", "body", "content"]}, {"name": "pass-fill", "title": "Pass fill", "categories": ["Real World"], "tags": ["pass"]}, {"name": "pass", "title": "Pass", "categories": ["Real World"], "tags": ["pass"]}, {"name": "passport-fill", "title": "Passport fill", "categories": ["Travel"], "tags": ["visa", "foreign"]}, {"name": "passport", "title": "Passport", "categories": ["Travel"], "tags": ["visa", "foreign"]}, {"name": "patch-check-fill", "title": "Patch check fill", "categories": ["Badges"], "tags": ["verified", "checkmark", "certified"]}, {"name": "patch-check", "title": "Patch check", "categories": ["Badges"], "tags": ["verified", "checkmark", "certified"]}, {"name": "patch-exclamation-fill", "title": "Patch exclamation fill", "categories": ["Badges"], "tags": ["excited", "certified"]}, {"name": "patch-exclamation", "title": "Patch exclamation", "categories": ["Badges"], "tags": ["excited", "certified"]}, {"name": "patch-minus-fill", "title": "Patch minus fill", "categories": ["Badges"], "tags": ["subtract"]}, {"name": "patch-minus", "title": "Patch minus", "categories": ["Badges"], "tags": ["subtract"]}, {"name": "patch-plus-fill", "title": "Patch plus fill", "categories": ["Badges"], "tags": ["add"]}, {"name": "patch-plus", "title": "Patch plus", "categories": ["Badges"], "tags": ["add"]}, {"name": "patch-question-fill", "title": "Patch question fill", "categories": ["Badges"], "tags": ["help"]}, {"name": "patch-question", "title": "Patch question", "categories": ["Badges"], "tags": ["help"]}, {"name": "pause-btn-fill", "title": "Pause btn fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "pause-btn", "title": "Pause btn", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "pause-circle-fill", "title": "Pause circle fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "pause-circle", "title": "Pause circle", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "pause-fill", "title": "Pause fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "pause", "title": "Pause", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "paypal", "title": "<PERSON><PERSON>", "categories": ["Brand"], "tags": ["social"]}, {"name": "pc-display-horizontal", "title": "PC display horizontal", "categories": ["Devices"], "tags": ["computer", "workstation", "desktop"]}, {"name": "pc-display", "title": "PC display", "categories": ["Devices"], "tags": ["computer", "workstation", "desktop"]}, {"name": "pc-horizontal", "title": "PC horizontal", "categories": ["Devices"], "tags": ["computer", "workstation", "desktop"]}, {"name": "pc", "title": "PC", "categories": ["Devices"], "tags": ["computer", "workstation", "desktop"]}, {"name": "pci-card-network", "title": "PCI card network", "categories": ["Devices"], "tags": ["card", "expansion", "ethernet", "wifi", "internet"]}, {"name": "pci-card-sound", "title": "PCI card sound", "categories": ["Devices"], "tags": ["card", "expansion", "audio"]}, {"name": "pci-card", "title": "PCI card", "categories": ["Devices"], "tags": ["card", "expansion"]}, {"name": "peace-fill", "title": "Peace fill", "categories": ["Miscellaneous"], "tags": ["peace", "love"]}, {"name": "peace", "title": "Peace", "categories": ["Miscellaneous"], "tags": ["peace", "love"]}, {"name": "pen-fill", "title": "Pen fill", "categories": ["Tools"], "tags": ["edit", "write", "ballpoint"]}, {"name": "pen", "title": "Pen", "categories": ["Tools"], "tags": ["edit", "write", "ballpoint"]}, {"name": "pencil-fill", "title": "Pencil fill", "categories": ["Tools"], "tags": ["edit", "write"]}, {"name": "pencil-square", "title": "Pencil square", "categories": ["Tools"], "tags": ["edit", "write"]}, {"name": "pencil", "title": "Pencil", "categories": ["Tools"], "tags": ["edit", "write"]}, {"name": "pentagon-fill", "title": "Pentagon fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "pentagon-half", "title": "Pentagon half", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "pentagon", "title": "Pentagon", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "polygon"]}, {"name": "people-fill", "title": "People fill", "categories": ["People"], "tags": ["member", "humans", "organization", "avatar", "users", "profile"]}, {"name": "people", "title": "People", "categories": ["People"], "tags": ["member", "humans", "organization", "avatar", "users", "profile"]}, {"name": "percent", "title": "Percent", "categories": ["Typography"], "tags": ["percentage", "math", "fraction"]}, {"name": "person-add", "title": "Person add", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-arms-up", "title": "Person arms up", "categories": ["People"], "tags": ["human", "man", "woman"]}, {"name": "person-badge-fill", "title": "Person badge fill", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "badge", "id", "card", "account"]}, {"name": "person-badge", "title": "Person badge", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "badge", "id", "card", "account", "profile"]}, {"name": "person-bounding-box", "title": "Person bounding box", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "crop", "account", "profile"]}, {"name": "person-check-fill", "title": "Person check fill", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "verified", "account"]}, {"name": "person-check", "title": "Person check", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "verified", "account", "profile"]}, {"name": "person-circle", "title": "People circle", "categories": ["People"], "tags": ["member", "humans", "organization", "avatar", "user", "account", "profile"]}, {"name": "person-dash-fill", "title": "Person dash fill", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "remove", "delete", "account"]}, {"name": "person-dash", "title": "Person dash", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "remove", "delete", "account"]}, {"name": "person-down", "title": "Person down", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-exclamation", "title": "Person exclamation", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill-add", "title": "Person fill add", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill-check", "title": "Person fill check", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill-dash", "title": "Person fill dash", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill-down", "title": "Person fill down", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill-exclamation", "title": "Person fill exclamation", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill-gear", "title": "Person fill gear", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill-lock", "title": "Person fill lock", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill-slash", "title": "Person fill slash", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill-up", "title": "Person fill up", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill-x", "title": "Person fill x", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-fill", "title": "Person fill", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account", "profile"]}, {"name": "person-gear", "title": "Person gear", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-heart", "title": "Person heart", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account", "love", "valentine", "romance"]}, {"name": "person-hearts", "title": "Person hearts", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account", "love", "valentine", "romance"]}, {"name": "person-lines-fill", "title": "Person lines fill", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "contact", "list", "account", "profile"]}, {"name": "person-lock", "title": "Person lock", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-plus-fill", "title": "Person plus fill", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "new", "add", "account"]}, {"name": "person-plus", "title": "Person plus", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "new", "add", "account"]}, {"name": "person-raised-hand", "title": "Person raised hand", "categories": ["People"], "tags": ["human", "man", "woman", "question", "help"]}, {"name": "person-rolodex", "title": "Person rolodex", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account", "contact"]}, {"name": "person-slash", "title": "Person slash", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-square", "title": "Person square", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account", "profile"]}, {"name": "person-standing-dress", "title": "Person standing dress", "categories": ["People"], "tags": ["human", "man", "woman"]}, {"name": "person-standing", "title": "Person standing", "categories": ["People"], "tags": ["human", "man", "woman"]}, {"name": "person-up", "title": "Person up", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account"]}, {"name": "person-vcard-fill", "title": "Person vcard fill", "categories": ["People"], "tags": ["human", "individual", "avatar", "user", "account"]}, {"name": "person-vcard", "title": "Person vcard", "categories": ["People"], "tags": ["human", "individual", "avatar", "user", "account"]}, {"name": "person-video", "title": "Person video", "categories": ["People"], "tags": ["human", "individual", "avatar", "user", "wfh"]}, {"name": "person-video2", "title": "Person video2", "categories": ["People"], "tags": ["human", "individual", "avatar", "user", "wfh"]}, {"name": "person-video3", "title": "Person video3", "categories": ["People"], "tags": ["human", "individual", "avatar", "user", "wfh"]}, {"name": "person-walking", "title": "Person walking", "categories": ["People"], "tags": ["human", "man", "woman"]}, {"name": "person-wheelchair", "title": "Person wheelchair", "categories": ["People"], "tags": ["human", "man", "woman", "accessibility", "a11y", "handicap"]}, {"name": "person-workspace", "title": "Person workspace", "categories": ["People"], "tags": ["human", "individual", "avatar", "user", "wfh"]}, {"name": "person-x-fill", "title": "Person x fill", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "remove", "delete", "account"]}, {"name": "person-x", "title": "Person x", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "remove", "delete", "account"]}, {"name": "person", "title": "Person", "categories": ["People"], "tags": ["member", "human", "individual", "avatar", "user", "account", "profile"]}, {"name": "phone-fill", "title": "Phone fill", "categories": ["Devices"], "tags": ["mobile", "telephone"]}, {"name": "phone-flip", "title": "Phone flip", "categories": ["Devices"], "tags": ["mobile", "telephone"]}, {"name": "phone-landscape-fill", "title": "Phone landscape fill", "categories": ["Devices"], "tags": ["mobile", "telephone"]}, {"name": "phone-landscape", "title": "Phone landscape", "categories": ["Devices"], "tags": ["mobile", "telephone"]}, {"name": "phone-vibrate-fill", "title": "Phone vibrate fill", "categories": ["Devices"], "tags": ["mobile", "telephone", "haptic"]}, {"name": "phone-vibrate", "title": "Phone vibrate", "categories": ["Devices"], "tags": ["mobile", "telephone", "haptic"]}, {"name": "phone", "title": "Phone", "categories": ["Devices"], "tags": ["mobile", "telephone"]}, {"name": "pie-chart-fill", "title": "Pie chart fill", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "pie-chart", "title": "Pie chart", "categories": ["Data"], "tags": ["chart", "graph", "analytics"]}, {"name": "piggy-bank-fill", "title": "Piggy bank fill", "categories": ["Commerce"], "tags": ["money", "finance", "banking", "savings"]}, {"name": "piggy-bank", "title": "Piggy bank", "categories": ["Commerce"], "tags": ["money", "finance", "banking", "savings"]}, {"name": "pin-angle-fill", "title": "Pin angle fill", "categories": ["Real world"], "tags": ["pushpin", "thumbtack"]}, {"name": "pin-angle", "title": "Pin angle", "categories": ["Real world"], "tags": ["pushpin", "thumbtack"]}, {"name": "pin-fill", "title": "Pin fill", "categories": ["Real world"], "tags": ["pushpin", "thumbtack"]}, {"name": "pin-map-fill", "title": "Pin map fill", "categories": ["Geo"], "tags": ["geography", "map", "pin", "location"]}, {"name": "pin-map", "title": "Pin map", "categories": ["Geo"], "tags": ["geography", "map", "pin", "location"]}, {"name": "pin", "title": "<PERSON>n", "categories": ["Real world"], "tags": ["pushpin", "thumbtack"]}, {"name": "pinterest", "title": "Pinterest", "categories": ["Brand"], "tags": ["social"]}, {"name": "pip-fill", "title": "Pip fill", "categories": ["Media"], "tags": ["picture", "tv", "television", "display", "nested"]}, {"name": "pip", "title": "<PERSON><PERSON>", "categories": ["Media"], "tags": ["picture", "tv", "television", "display", "nested"]}, {"name": "play-btn-fill", "title": "Play btn fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "play-btn", "title": "Play btn", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "play-circle-fill", "title": "Play circle fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "play-circle", "title": "Play circle", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "play-fill", "title": "Play fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "play", "title": "Play", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "playstation", "title": "Playstation", "categories": ["Brand"], "tags": ["sony", "gaming"]}, {"name": "plug-fill", "title": "Plug fill", "categories": ["Real world"], "tags": ["power", "outlet"]}, {"name": "plug", "title": "Plug", "categories": ["Real world"], "tags": ["power", "outlet"]}, {"name": "plugin", "title": "Plugin", "categories": ["UI"], "tags": ["addon", "software"]}, {"name": "plus-circle-dotted", "title": "Plus circle dotted", "categories": ["Alerts, warnings, and signs"], "tags": ["add", "new"]}, {"name": "plus-circle-fill", "title": "Plus circle fill", "categories": ["Alerts, warnings, and signs"], "tags": ["add", "new"]}, {"name": "plus-circle", "title": "Plus circle", "categories": ["Alerts, warnings, and signs"], "tags": ["add", "new"]}, {"name": "plus-lg", "title": "Plus lg", "categories": ["Alerts, warnings, and signs"], "tags": ["add", "new"]}, {"name": "plus-slash-minus", "title": "Plus slash minus", "categories": ["Typography"], "tags": ["difference", "add", "subtract"]}, {"name": "plus-square-dotted", "title": "Plus square dotted", "categories": ["Alerts, warnings, and signs"], "tags": ["add", "new"]}, {"name": "plus-square-fill", "title": "Plus square fill", "categories": ["Alerts, warnings, and signs"], "tags": ["add", "new"]}, {"name": "plus-square", "title": "Plus square", "categories": ["Alerts, warnings, and signs"], "tags": ["add", "new"]}, {"name": "plus", "title": "Plus", "categories": ["Alerts, warnings, and signs"], "tags": ["add", "new"]}, {"name": "postage-fill", "title": "Postage fill", "categories": ["Real World"], "tags": ["mail", "stamp", "shipping"]}, {"name": "postage-heart-fill", "title": "Postage heart fill", "categories": ["Real World", "Love"], "tags": ["mail", "stamp", "love", "valentine", "romance"]}, {"name": "postage-heart", "title": "Postage heart", "categories": ["Real World", "Love"], "tags": ["mail", "stamp", "love", "valentine", "romance"]}, {"name": "postage", "title": "Postage", "categories": ["Real World"], "tags": ["stamp", "shipping"]}, {"name": "postcard-fill", "title": "Postcard fill", "categories": ["Real World"], "tags": ["mail", "letter"]}, {"name": "postcard-heart-fill", "title": "Postcard heart fill", "categories": ["Real World", "Love"], "tags": ["mail", "letter", "love", "valentine", "romance"]}, {"name": "postcard-heart", "title": "Postcard heart", "categories": ["Real World", "Love"], "tags": ["mail", "letter", "love", "valentine", "romance"]}, {"name": "postcard", "title": "Postcard", "categories": ["Real World"], "tags": ["mail", "letter"]}, {"name": "power", "title": "Power", "categories": ["UI and keyboard"], "tags": ["off", "on"]}, {"name": "prescription", "title": "Prescription", "categories": ["Medical"], "tags": ["rx", "pills", "capsules", "medicine"]}, {"name": "prescription2", "title": "Prescription2", "categories": ["Medical"], "tags": ["rx", "pills", "capsules", "medicine"]}, {"name": "printer-fill", "title": "Printer fill", "categories": ["Devices"], "tags": ["print"]}, {"name": "printer", "title": "Printer", "categories": ["Devices"], "tags": ["print"]}, {"name": "projector-fill", "title": "Projector fill", "categories": ["Devices"], "tags": ["projection", "present", "screen"]}, {"name": "projector", "title": "Projector", "categories": ["Devices"], "tags": ["projection", "present", "screen"]}, {"name": "puzzle-fill", "title": "Puzzle fill", "categories": ["Miscellaneous"], "tags": ["puzzle", "piece"]}, {"name": "puzzle", "title": "Puzzle", "categories": ["Miscellaneous"], "tags": ["puzzle", "piece"]}, {"name": "qr-code-scan", "title": "QR code scan", "categories": ["Communications"], "tags": ["scan"]}, {"name": "qr-code", "title": "QR code", "categories": ["Communications"], "tags": ["scan"]}, {"name": "question-circle-fill", "title": "Question fill", "categories": ["Alerts, warnings, and signs"], "tags": ["help"]}, {"name": "question-circle", "title": "Question circle", "categories": ["Alerts, warnings, and signs"], "tags": ["help"]}, {"name": "question-diamond-fill", "title": "Question diamond fill", "categories": ["Alerts, warnings, and signs"], "tags": ["help"]}, {"name": "question-diamond", "title": "Question diamond", "categories": ["Alerts, warnings, and signs"], "tags": ["help"]}, {"name": "question-lg", "title": "Question lg", "categories": ["Alerts, warnings, and signs"], "tags": ["help"]}, {"name": "question-octagon-fill", "title": "Question octagon fill", "categories": ["Alerts, warnings, and signs"], "tags": ["help"]}, {"name": "question-octagon", "title": "Question octagon", "categories": ["Alerts, warnings, and signs"], "tags": ["help"]}, {"name": "question-square-fill", "title": "Question square fill", "categories": ["Alerts, warnings, and signs"], "tags": ["help"]}, {"name": "question-square", "title": "Question square", "categories": ["Alerts, warnings, and signs"], "tags": ["help"]}, {"name": "question", "title": "Question", "categories": ["Alerts, warnings, and signs"], "tags": ["help"]}, {"name": "quora", "title": "<PERSON><PERSON><PERSON>", "categories": ["Brand"], "tags": ["social"]}, {"name": "quote", "title": "Quote", "categories": ["Typography"], "tags": ["blockquote", "quotes", "quotation"]}, {"name": "r-circle-fill", "title": "R circle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["registered", "trademark"]}, {"name": "r-circle", "title": "R circle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["registered", "trademark"]}, {"name": "r-square-fill", "title": "R square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["registered", "trademark"]}, {"name": "r-square", "title": "R square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["registered", "trademark"]}, {"name": "radar", "title": "Radar", "categories": ["Geo"], "tags": ["geography", "map", "pin", "location"]}, {"name": "radioactive", "title": "Radioactive", "categories": ["Real World"], "tags": ["radiation", "nuclear", "danger"]}, {"name": "rainbow", "title": "Rainbow", "categories": ["Weather"], "tags": ["colors", "arches"]}, {"name": "receipt-cutoff", "title": "Receipt cutoff", "categories": ["Commerce"], "tags": ["receipt", "invoice", "sale", "purchase", "bill"]}, {"name": "receipt", "title": "Receipt", "categories": ["Commerce"], "tags": ["receipt", "invoice", "sale", "purchase", "bill"]}, {"name": "reception-0", "title": "Reception 0", "categories": ["Communications"], "tags": ["reception", "cellphone", "mobile", "carrier", "network", "signal"]}, {"name": "reception-1", "title": "Reception 1", "categories": ["Communications"], "tags": ["reception", "cellphone", "mobile", "carrier", "network", "signal"]}, {"name": "reception-2", "title": "Reception 2", "categories": ["Communications"], "tags": ["reception", "cellphone", "mobile", "carrier", "network", "signal"]}, {"name": "reception-3", "title": "Reception 3", "categories": ["Communications"], "tags": ["reception", "cellphone", "mobile", "carrier", "network", "signal"]}, {"name": "reception-4", "title": "Reception 4", "categories": ["Communications"], "tags": ["reception", "cellphone", "mobile", "carrier", "network", "signal"]}, {"name": "record-btn-fill", "title": "Record btn fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "record-btn", "title": "Record btn", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "record-circle-fill", "title": "Record circle fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "record-circle", "title": "Record circle", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "record-fill", "title": "Record fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "record", "title": "Record", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "record2-fill", "title": "Record2 fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "record2", "title": "Record2", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "recycle", "title": "Recycle", "categories": ["Arrows"], "tags": ["recyling", "trash"]}, {"name": "reddit", "title": "Reddit", "categories": ["Brand"], "tags": ["social"]}, {"name": "regex", "title": "Regex", "categories": ["Typography"], "tags": ["text", "type", "code", "developer", "development", "software", "regular expression"]}, {"name": "repeat-1", "title": "Repeat 1", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "repeat", "title": "Repeat", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "reply-all-fill", "title": "Reply all fill", "categories": ["Communications"], "tags": ["mail", "email"]}, {"name": "reply-all", "title": "Reply all", "categories": ["Communications"], "tags": ["mail", "email"]}, {"name": "reply-fill", "title": "Reply fill", "categories": ["Communications"], "tags": ["mail", "email"]}, {"name": "reply", "title": "Reply", "categories": ["Communications"], "tags": ["mail", "email"]}, {"name": "rewind-btn-fill", "title": "Rewind btn fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "rewind-btn", "title": "Rewind btn", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "rewind-circle-fill", "title": "Rewind circle fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "rewind-circle", "title": "Rewind circle", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "rewind-fill", "title": "Rewind fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "rewind", "title": "Rewind", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "robot", "title": "Robot", "categories": ["Devices"], "tags": ["bot"]}, {"name": "rocket-fill", "title": "Rocket fill", "categories": ["Real world"], "tags": ["ship", "rocketship", "spaceship"]}, {"name": "rocket-takeoff-fill", "title": "Rocket takeoff fill", "categories": ["Real world"], "tags": ["ship", "rocketship", "spaceship", "launch"]}, {"name": "rocket-takeoff", "title": "Rocket takeoff", "categories": ["Real world"], "tags": ["ship", "rocketship", "spaceship", "launch"]}, {"name": "rocket", "title": "Rocket", "categories": ["Real world"], "tags": ["ship", "rocketship", "spaceship"]}, {"name": "router-fill", "title": "Router fill", "categories": ["Devices"], "tags": ["wifi", "internet", "wireless", "network"]}, {"name": "router", "title": "Router", "categories": ["Devices"], "tags": ["wifi", "internet", "wireless", "network"]}, {"name": "rss-fill", "title": "RSS fill", "categories": ["Communications"], "tags": ["atom", "feed", "xml"]}, {"name": "rss", "title": "RSS", "categories": ["Communications"], "tags": ["atom", "feed", "xml"]}, {"name": "rulers", "title": "Rulers", "categories": ["Graphics"], "tags": ["measure", "guide"]}, {"name": "safe-fill", "title": "Safe fill", "categories": ["Real world"], "tags": ["vault", "bank", "finance"]}, {"name": "safe", "title": "Safe", "categories": ["Real world"], "tags": ["vault", "bank", "finance"]}, {"name": "safe2-fill", "title": "Safe2 fill", "categories": ["Real world"], "tags": ["vault", "bank", "finance"]}, {"name": "safe2", "title": "Safe2", "categories": ["Real world"], "tags": ["vault", "bank", "finance"]}, {"name": "save-fill", "title": "Save fill", "categories": ["UI and keyboard"], "tags": ["save", "floppy"]}, {"name": "save", "title": "Save", "categories": ["UI and keyboard"], "tags": ["save", "floppy"]}, {"name": "save2-fill", "title": "Save2 fill", "categories": ["UI and keyboard"], "tags": ["save", "floppy"]}, {"name": "save2", "title": "Save2", "categories": ["UI and keyboard"], "tags": ["save", "floppy"]}, {"name": "scissors", "title": "Scissors", "categories": ["Real world"], "tags": ["cut", "shears"]}, {"name": "scooter", "title": "<PERSON>ooter", "categories": ["Transportation"], "tags": ["riding"]}, {"name": "screwdriver", "title": "<PERSON><PERSON><PERSON><PERSON>", "categories": ["Tools"], "tags": ["tool"]}, {"name": "sd-card-fill", "title": "SD card fill", "categories": ["Devices"], "tags": ["storage", "microsd", "chip", "memory"]}, {"name": "sd-card", "title": "SD card", "categories": ["Devices"], "tags": ["storage", "microsd", "chip", "memory"]}, {"name": "search-heart-fill", "title": "Search heart fill", "categories": ["Communications", "Love"], "tags": ["magnifying-glass", "look", "love", "romance", "valentine"]}, {"name": "search-heart", "title": "Search heart", "categories": ["Communications", "Love"], "tags": ["magnifying-glass", "look", "love", "romance", "valentine"]}, {"name": "search", "title": "Search", "categories": ["Communications"], "tags": ["magnifying-glass", "look"]}, {"name": "segmented-nav", "title": "Segmented nav", "categories": ["Controls"], "tags": ["nav", "tabs", "tabbed", "app", "ui"]}, {"name": "send-arrow-down-fill", "title": "Send arrow down fill", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-arrow-down", "title": "Send arrow down", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-arrow-up-fill", "title": "Send arrow up fill", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-arrow-up", "title": "Send arrow up", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-check-fill", "title": "Send check fill", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-check", "title": "Send check", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-dash-fill", "title": "Send dash fill", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-dash", "title": "Send dash", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-exclamation-fill", "title": "Send exclamation fill", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-exclamation", "title": "Send exclamation", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-fill", "title": "Send fill", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-plus-fill", "title": "Send plus fill", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-plus", "title": "Send plus", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-slash-fill", "title": "Send slash fill", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-slash", "title": "Send slash", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-x-fill", "title": "Send x fill", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send-x", "title": "Send x", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "send", "title": "Send", "categories": ["Communications"], "tags": ["message", "sending", "sent", "paper-plane"]}, {"name": "server", "title": "Server", "categories": ["Devices"], "tags": ["server", "network"]}, {"name": "shadows", "title": "Shadows", "categories": ["Graphics"], "tags": ["photo", "editing", "image", "picture"]}, {"name": "share-fill", "title": "Share fill", "categories": ["Communications"], "tags": ["share", "link"]}, {"name": "share", "title": "Share", "categories": ["Communications"], "tags": ["share", "link"]}, {"name": "shield-check", "title": "Shield check", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shield-exclamation", "title": "Shield exclamation", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shield-fill-check", "title": "Shield fill check", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shield-fill-exclamation", "title": "Shield fill exclamation", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shield-fill-minus", "title": "Shield fill minus", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shield-fill-plus", "title": "Shield fill plus", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shield-fill-x", "title": "Shield fill x", "categories": ["Security"], "tags": ["privacy", "security", "remove", "delete"]}, {"name": "shield-fill", "title": "Shield fill", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shield-lock-fill", "title": "Shield lock fill", "categories": ["Security"], "tags": ["privacy", "security", "lock"]}, {"name": "shield-lock", "title": "Shield lock", "categories": ["Security"], "tags": ["privacy", "security", "lock"]}, {"name": "shield-minus", "title": "Shield minus", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shield-plus", "title": "Shield plus", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shield-shaded", "title": "Shield shaded", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shield-slash-fill", "title": "Shield slash fill", "categories": ["Security"], "tags": ["shield", "badge"]}, {"name": "shield-slash", "title": "Shield slash", "categories": ["Security"], "tags": ["shield", "badge"]}, {"name": "shield-x", "title": "Shield x", "categories": ["Security"], "tags": ["privacy", "security", "remove", "delete"]}, {"name": "shield", "title": "Shield", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "shift-fill", "title": "Shift fill", "categories": ["UI and keyboard"], "tags": ["key"]}, {"name": "shift", "title": "Shift", "categories": ["UI and keyboard"], "tags": ["key"]}, {"name": "shop-window", "title": "Shop window", "categories": ["Commerce"], "tags": ["shop", "store", "market", "marketplace", "shopping", "retail"]}, {"name": "shop", "title": "Shop", "categories": ["Commerce"], "tags": ["shop", "store", "market", "marketplace", "shopping", "retail"]}, {"name": "shuffle", "title": "Shuffle", "categories": ["Arrows"], "tags": ["shuffle", "random"]}, {"name": "sign-dead-end-fill", "title": "Sign dead end fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-dead-end", "title": "Sign dead end", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-do-not-enter-fill", "title": "Sign do not enter fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-do-not-enter", "title": "Sign do not enter", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-intersection-fill", "title": "Sign intersection fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-intersection-side-fill", "title": "Sign intersection side fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-intersection-side", "title": "Sign intersection side", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-intersection-t-fill", "title": "Sign intersection t fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-intersection-t", "title": "Sign intersection t", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-intersection-y-fill", "title": "Sign intersection y fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-intersection-y", "title": "Sign intersection y", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-intersection", "title": "Sign intersection", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-merge-left-fill", "title": "Sign merge left fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-merge-left", "title": "Sign merge left", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-merge-right-fill", "title": "Sign merge right fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-merge-right", "title": "Sign merge right", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-no-left-turn-fill", "title": "Sign no left turn fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-no-left-turn", "title": "Sign no left turn", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-no-parking-fill", "title": "Sign no parking fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-no-parking", "title": "Sign no parking", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-no-right-turn-fill", "title": "Sign no right turn fill", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-no-right-turn", "title": "Sign no right turn", "categories": ["Transportation"], "tags": ["road", "driving", "directions"]}, {"name": "sign-railroad-fill", "title": "Sign railroad fill", "categories": ["Transportation"], "tags": ["road", "driving", "train", "tracks"]}, {"name": "sign-railroad", "title": "Sign railroad", "categories": ["Transportation"], "tags": ["road", "driving", "train", "tracks"]}, {"name": "sign-stop-fill", "title": "Sign stop fill", "categories": ["Transportation"], "tags": ["stop sign", "intersection", "road", "driving"]}, {"name": "sign-stop-lights-fill", "title": "Sign stop lights fill", "categories": ["Transportation"], "tags": ["stop sign", "intersection", "road", "driving"]}, {"name": "sign-stop-lights", "title": "Sign stop lights", "categories": ["Transportation"], "tags": ["stop sign", "intersection", "road", "driving"]}, {"name": "sign-stop", "title": "Sign stop", "categories": ["Transportation"], "tags": ["stop sign", "intersection", "road", "driving"]}, {"name": "sign-turn-left-fill", "title": "Sign turn left fill", "categories": ["Transportation"], "tags": ["road", "driving", "navigate", "navigation", "route"]}, {"name": "sign-turn-left", "title": "Sign turn left", "categories": ["Transportation"], "tags": ["road", "driving", "navigate", "navigation", "route"]}, {"name": "sign-turn-right-fill", "title": "Sign turn right fill", "categories": ["Transportation"], "tags": ["road", "driving", "navigate", "navigation", "route"]}, {"name": "sign-turn-right", "title": "Sign turn right", "categories": ["Transportation"], "tags": ["road", "driving", "navigate", "navigation", "route"]}, {"name": "sign-turn-slight-left-fill", "title": "Sign turn slight left fill", "categories": ["Transportation"], "tags": ["road", "driving", "navigate", "navigation", "route"]}, {"name": "sign-turn-slight-left", "title": "Sign turn slight left", "categories": ["Transportation"], "tags": ["road", "driving", "navigate", "navigation", "route"]}, {"name": "sign-turn-slight-right-fill", "title": "Sign turn slight right fill", "categories": ["Transportation"], "tags": ["road", "driving", "navigate", "navigation", "route"]}, {"name": "sign-turn-slight-right", "title": "Sign turn slight right", "categories": ["Transportation"], "tags": ["road", "driving", "navigate", "navigation", "route"]}, {"name": "sign-yield-fill", "title": "Sign yield fill", "categories": ["Transportation"], "tags": ["road", "driving", "intersection"]}, {"name": "sign-yield", "title": "Sign yield", "categories": ["Transportation"], "tags": ["road", "driving", "intersection"]}, {"name": "signal", "title": "Signal", "categories": ["Brand"], "tags": ["social"]}, {"name": "signpost-2-fill", "title": "Signpost 2 fill", "categories": ["Real world"], "tags": ["milestone", "sign", "road sign", "street sign", "directions"]}, {"name": "signpost-2", "title": "Signpost 2", "categories": ["Real world"], "tags": ["milestone", "sign", "road sign", "street sign", "directions"]}, {"name": "signpost-fill", "title": "Signpost fill", "categories": ["Real world"], "tags": ["milestone", "sign", "road sign", "street sign", "directions"]}, {"name": "signpost-split-fill", "title": "Signpost split fill", "categories": ["Real world"], "tags": ["milestone", "sign", "road sign", "street sign", "directions"]}, {"name": "signpost-split", "title": "Signpost split", "categories": ["Real world"], "tags": ["milestone", "sign", "road sign", "street sign", "directions"]}, {"name": "signpost", "title": "Signpost", "categories": ["Real world"], "tags": ["milestone", "sign", "road sign", "street sign", "directions"]}, {"name": "sim-fill", "title": "Sim fill", "categories": ["Devices"], "tags": ["mobile", "carrier"]}, {"name": "sim-slash-fill", "title": "Sim slash fill", "categories": ["Devices"], "tags": ["mobile", "carrier"]}, {"name": "sim-slash", "title": "<PERSON>m slash", "categories": ["Devices"], "tags": ["mobile", "carrier"]}, {"name": "sim", "title": "<PERSON>m", "categories": ["Devices"], "tags": ["mobile", "carrier"]}, {"name": "sina-weibo", "title": "<PERSON><PERSON>", "categories": ["Brand"], "tags": ["social"]}, {"name": "skip-backward-btn-fill", "title": "Skip backward btn fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-backward-btn", "title": "<PERSON><PERSON> backward btn", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-backward-circle-fill", "title": "Skip backward circle fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-backward-circle", "title": "Skip backward circle", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-backward-fill", "title": "Skip backward fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-backward", "title": "<PERSON>p backward", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-end-btn-fill", "title": "Skip end btn fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-end-btn", "title": "Skip end btn", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-end-circle-fill", "title": "Skip end circle fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-end-circle", "title": "Skip end circle", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-end-fill", "title": "Skip end fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-end", "title": "Skip end", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-forward-btn-fill", "title": "Skip forward btn fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-forward-btn", "title": "Skip forward btn", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-forward-circle-fill", "title": "Skip forward circle fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-forward-circle", "title": "Skip forward circle", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-forward-fill", "title": "Skip forward fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-forward", "title": "Skip forward", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-start-btn-fill", "title": "Skip start btn fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-start-btn", "title": "Skip start btn", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-start-circle-fill", "title": "Skip start circle fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-start-circle", "title": "Skip start circle", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-start-fill", "title": "Skip start fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skip-start", "title": "Skip start", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "skype", "title": "Skype", "categories": ["Brand"], "tags": ["social", "microsoft"]}, {"name": "slack", "title": "<PERSON><PERSON>ck", "categories": ["Brand"], "tags": ["social"]}, {"name": "slash-circle-fill", "title": "Slash circle fill", "categories": ["Alerts, warnings, and signs"], "tags": ["shape", "stop", "ban", "no"]}, {"name": "slash-circle", "title": "Circle slash", "categories": ["Alerts, warnings, and signs"], "tags": ["shape", "stop", "ban", "no"]}, {"name": "slash-lg", "title": "Slash lg", "categories": ["Alerts, warnings, and signs"], "tags": ["shape", "stop", "ban", "no"]}, {"name": "slash-square-fill", "title": "Slash square fill", "categories": ["Alerts, warnings, and signs"], "tags": ["shape", "stop", "ban", "no"]}, {"name": "slash-square", "title": "Slash square", "categories": ["Alerts, warnings, and signs"], "tags": ["shape", "stop", "ban", "no"]}, {"name": "slash", "title": "Slash", "categories": ["Alerts, warnings, and signs"], "tags": ["shape", "stop", "ban", "no"]}, {"name": "sliders", "title": "Sliders", "categories": ["Graphics"], "tags": ["equalizer", "settings", "preferences", "dials"]}, {"name": "sliders2-vertical", "title": "Sliders2 vertical", "categories": ["Graphics"], "tags": ["equalizer", "settings", "preferences", "dials"]}, {"name": "sliders2", "title": "Sliders2", "categories": ["Graphics"], "tags": ["equalizer", "settings", "preferences", "dials"]}, {"name": "smartwatch", "title": "Smartwatch", "categories": ["Devices"], "tags": ["watch", "wearables"]}, {"name": "snapchat", "title": "Snapchat", "categories": ["Brand"], "tags": ["social"]}, {"name": "snow", "title": "Snow", "categories": ["Weather"], "tags": ["blizzard", "flurries"]}, {"name": "snow2", "title": "Snow2", "categories": ["Weather"], "tags": ["blizzard", "flurries"]}, {"name": "snow3", "title": "Snow3", "categories": ["Weather"], "tags": ["blizzard", "flurries"]}, {"name": "sort-alpha-down-alt", "title": "Sort alpha down alt", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-alpha-down", "title": "Sort alpha down", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-alpha-up-alt", "title": "Sort alpha up alt", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-alpha-up", "title": "Sort alpha up", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-down-alt", "title": "Sort down alt", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-down", "title": "Sort down", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-numeric-down-alt", "title": "Sort numeric down alt", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-numeric-down", "title": "Sort numeric down", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-numeric-up-alt", "title": "Sort numeric up alt", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-numeric-up", "title": "Sort numeric up", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-up-alt", "title": "Sort up alt", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "sort-up", "title": "Sort up", "categories": ["Sort and filter"], "tags": ["sort", "filter", "organize"]}, {"name": "soundwave", "title": "Soundwave", "categories": ["Media"], "tags": ["audio", "sound", "wave"]}, {"name": "sourceforge", "title": "Sourceforge", "categories": ["Brand"], "tags": ["social", "vcs", "git"]}, {"name": "speaker-fill", "title": "Speaker fill", "categories": ["Devices"], "tags": ["audio", "sound"]}, {"name": "speaker", "title": "Speaker", "categories": ["Devices"], "tags": ["audio", "sound"]}, {"name": "speedometer", "title": "Speedometer", "categories": ["Real world"], "tags": ["speed", "tachometer", "dashboard", "gauge"]}, {"name": "speedometer2", "title": "Speedometer2", "categories": ["Real world"], "tags": ["speed", "tachometer", "dashboard", "gauge"]}, {"name": "spellcheck", "title": "Spellcheck", "categories": ["Typography"], "tags": ["spelling", "grammar"]}, {"name": "spotify", "title": "Spotify", "categories": ["Brand"], "tags": ["social"]}, {"name": "square-fill", "title": "Square fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "rectangle"]}, {"name": "square-half", "title": "Square half fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "rectangle"]}, {"name": "square", "title": "Square", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "rectangle"]}, {"name": "stack-overflow", "title": "Stack overflow", "categories": ["Brand"], "tags": ["social"]}, {"name": "stack", "title": "<PERSON><PERSON>", "categories": ["Graphics"], "tags": ["layers"]}, {"name": "star-fill", "title": "Star fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "like", "favorite"]}, {"name": "star-half", "title": "Star half fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "like", "favorite"]}, {"name": "star", "title": "Star", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape", "like", "favorite"]}, {"name": "stars", "title": "Stars", "categories": ["Weather"], "tags": ["clear", "skies", "night"]}, {"name": "steam", "title": "Steam", "categories": ["Brand"], "tags": ["gaming"]}, {"name": "stickies-fill", "title": "Stickies fill", "categories": ["Real world"], "tags": ["postit", "note", "sticky"]}, {"name": "stickies", "title": "Stickies", "categories": ["Real world"], "tags": ["postit", "note", "sticky"]}, {"name": "sticky-fill", "title": "Sticky fill", "categories": ["Real world"], "tags": ["postit", "note", "sticky"]}, {"name": "sticky", "title": "<PERSON>y", "categories": ["Real world"], "tags": ["postit", "note", "sticky"]}, {"name": "stop-btn-fill", "title": "Stop btn fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "stop-btn", "title": "Stop btn", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "stop-circle-fill", "title": "Stop circle fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "stop-circle", "title": "Stop circle", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "stop-fill", "title": "Stop fill", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "stop", "title": "Stop", "categories": ["Media"], "tags": ["audio", "video", "av"]}, {"name": "stoplights-fill", "title": "Stoplights fill", "categories": ["Real world"], "tags": ["traffic", "lights", "intersection"]}, {"name": "stoplights", "title": "Stoplights", "categories": ["Real world"], "tags": ["traffic", "lights", "intersection"]}, {"name": "stopwatch-fill", "title": "Stopwatch fill", "categories": ["Devices"], "tags": ["time", "timer", "clock"]}, {"name": "stopwatch", "title": "Stopwatch", "categories": ["Devices"], "tags": ["time", "timer", "clock"]}, {"name": "strava", "title": "Strava", "categories": ["Brand"], "tags": ["social"]}, {"name": "stripe", "title": "Stripe", "categories": ["Brand"], "tags": ["payments", "commerce"]}, {"name": "subscript", "title": "Subscript", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "substack", "title": "Substack", "categories": ["Brand"], "tags": ["social", "blog"]}, {"name": "subtract", "title": "Subtract", "categories": ["Graphics"], "tags": ["graphics", "vector", "merge", "layers"]}, {"name": "suit-club-fill", "title": "Suit club fill", "categories": ["Entertainment"], "tags": ["card", "cards", "suit", "deck", "gambling"]}, {"name": "suit-club", "title": "Suit club", "categories": ["Entertainment"], "tags": ["card", "cards", "suit", "deck", "gambling"]}, {"name": "suit-diamond-fill", "title": "Suit diamond fill", "categories": ["Entertainment"], "tags": ["card", "cards", "suit", "deck", "gambling"]}, {"name": "suit-diamond", "title": "Suit diamond", "categories": ["Entertainment"], "tags": ["card", "cards", "suit", "deck", "gambling"]}, {"name": "suit-heart-fill", "title": "Suit heart fill", "categories": ["Entertainment"], "tags": ["card", "cards", "suit", "deck", "gambling"]}, {"name": "suit-heart", "title": "Suit heart", "categories": ["Entertainment"], "tags": ["card", "cards", "suit", "deck", "gambling"]}, {"name": "suit-spade-fill", "title": "Suit spade fill", "categories": ["Entertainment"], "tags": ["card", "cards", "suit", "deck", "gambling"]}, {"name": "suit-spade", "title": "Suit spade", "categories": ["Entertainment"], "tags": ["card", "cards", "suit", "deck", "gambling"]}, {"name": "suitcase-fill", "title": "Suitcase fill", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on"]}, {"name": "suitcase-lg-fill", "title": "Suitcase lg fill", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on"]}, {"name": "suitcase-lg", "title": "Suitcase lg", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on"]}, {"name": "suitcase", "title": "Suitcase", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on"]}, {"name": "suitcase2-fill", "title": "Suitcase2 fill", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on"]}, {"name": "suitcase2", "title": "Suitcase2", "categories": ["Travel"], "tags": ["luggage", "bags", "carry-on"]}, {"name": "sun-fill", "title": "Sun fill", "categories": ["Weather"], "tags": ["solar", "weather"]}, {"name": "sun", "title": "Sun", "categories": ["Weather"], "tags": ["solar", "weather"]}, {"name": "sunglasses", "title": "Sunglasses", "categories": ["Real world"], "tags": ["shades", "cool", "aviators"]}, {"name": "sunrise-fill", "title": "Sunrise fill", "categories": ["Weather"], "tags": ["dawn"]}, {"name": "sunrise", "title": "Sunrise", "categories": ["Weather"], "tags": ["dawn"]}, {"name": "sunset-fill", "title": "Sunset fill", "categories": ["Weather"], "tags": ["dusk"]}, {"name": "sunset", "title": "Sunset", "categories": ["Weather"], "tags": ["dusk"]}, {"name": "superscript", "title": "Superscript", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "symmetry-horizontal", "title": "Symmetry horizontal", "categories": ["Graphics"], "tags": ["align", "orientation", "mirror"]}, {"name": "symmetry-vertical", "title": "Symmetry vertical", "categories": ["Graphics"], "tags": ["align", "orientation", "mirror"]}, {"name": "table", "title": "Table", "categories": ["Files and folders"], "tags": ["spreadsheet"]}, {"name": "tablet-fill", "title": "Tablet fill", "categories": ["Devices"], "tags": ["mobile"]}, {"name": "tablet-landscape-fill", "title": "Tablet landscape fill", "categories": ["Devices"], "tags": ["mobile"]}, {"name": "tablet-landscape", "title": "Tablet landscape", "categories": ["Devices"], "tags": ["mobile"]}, {"name": "tablet", "title": "Tablet", "categories": ["Devices"], "tags": ["mobile"]}, {"name": "tag-fill", "title": "Tag fill", "categories": ["Real world"], "tags": ["price", "category", "taxonomy", "label"]}, {"name": "tag", "title": "Tag", "categories": ["Real world"], "tags": ["price", "category", "taxonomy", "label"]}, {"name": "tags-fill", "title": "Tags fill", "categories": ["Real world"], "tags": ["price", "category", "taxonomy", "label"]}, {"name": "tags", "title": "Tags", "categories": ["Real world"], "tags": ["price", "category", "taxonomy", "label"]}, {"name": "taxi-front-fill", "title": "Taxi front fill", "categories": ["Transportation"], "tags": ["cab", "uber", "lyft"]}, {"name": "taxi-front", "title": "Taxi front", "categories": ["Transportation"], "tags": ["cab", "uber", "lyft"]}, {"name": "telegram", "title": "Telegram", "categories": ["Brand"], "tags": ["social", "chat"]}, {"name": "telephone-fill", "title": "Telephone fill", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-forward-fill", "title": "Telephone forward fill", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-forward", "title": "Telephone forward", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-inbound-fill", "title": "Telephone inbound fill", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-inbound", "title": "Telephone inbound", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-minus-fill", "title": "Telephone minus fill", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-minus", "title": "Telephone minus", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-outbound-fill", "title": "Telephone outbound fill", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-outbound", "title": "Telephone outbound", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-plus-fill", "title": "Telephone plus fill", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-plus", "title": "Telephone plus", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-x-fill", "title": "Telephone x fill", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone-x", "title": "Telephone x", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "telephone", "title": "Telephone", "categories": ["Communications"], "tags": ["telephone", "phone", "call"]}, {"name": "tencent-qq", "title": "Tencent QQ", "categories": ["Brand"], "tags": ["social"]}, {"name": "terminal-dash", "title": "Terminal dash", "categories": ["Apps"], "tags": ["command-line", "cli", "command-prompt"]}, {"name": "terminal-fill", "title": "Terminal fill", "categories": ["Apps"], "tags": ["command-line", "cli", "command-prompt"]}, {"name": "terminal-plus", "title": "Terminal plus", "categories": ["Apps"], "tags": ["command-line", "cli", "command-prompt"]}, {"name": "terminal-split", "title": "Terminal split", "categories": ["Apps"], "tags": ["command-line", "cli", "command-prompt"]}, {"name": "terminal-x", "title": "Terminal x", "categories": ["Apps"], "tags": ["command-line", "cli", "command-prompt"]}, {"name": "terminal", "title": "Terminal", "categories": ["Apps"], "tags": ["command-line", "cli", "command-prompt"]}, {"name": "text-center", "title": "Text center", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment"]}, {"name": "text-indent-left", "title": "Text indent left", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment"]}, {"name": "text-indent-right", "title": "Text indent right", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment"]}, {"name": "text-left", "title": "Text left", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment"]}, {"name": "text-paragraph", "title": "Text paragraph", "categories": ["Typography"], "tags": ["text", "type", "paragraph", "copy"]}, {"name": "text-right", "title": "Text right", "categories": ["Typography"], "tags": ["text", "type", "justify", "alignment"]}, {"name": "text-wrap", "title": "Text wrap", "categories": ["Typography"], "tags": ["text", "type", "word wrap"]}, {"name": "textarea-resize", "title": "Textarea resize", "categories": ["Graphics"], "tags": ["text", "form"]}, {"name": "textarea-t", "title": "Textarea t", "categories": ["Graphics"], "tags": ["text", "insert", "bounding-box"]}, {"name": "textarea", "title": "Textarea", "categories": ["Graphics"], "tags": ["text", "insert", "bounding-box"]}, {"name": "thermometer-half", "title": "Thermometer half", "categories": ["Weather"], "tags": ["temperature", "weather"]}, {"name": "thermometer-high", "title": "Thermometer high", "categories": ["Weather"], "tags": ["temperature", "weather"]}, {"name": "thermometer-low", "title": "Thermometer low", "categories": ["Weather"], "tags": ["temperature", "weather"]}, {"name": "thermometer-snow", "title": "Thermometer snow", "categories": ["Weather"], "tags": ["temperature", "weather"]}, {"name": "thermometer-sun", "title": "Thermometer sun", "categories": ["Weather"], "tags": ["temperature", "weather"]}, {"name": "thermometer", "title": "Thermometer", "categories": ["Real world"], "tags": ["temperature", "weather"]}, {"name": "threads-fill", "title": "Threads fill", "categories": ["Brand"], "tags": ["social", "chat", "instagram", "meta"]}, {"name": "threads", "title": "Threads", "categories": ["Brand"], "tags": ["social", "chat", "instagram", "meta"]}, {"name": "three-dots-vertical", "title": "Three dots vertical", "categories": ["Controls"], "tags": ["kebab", "more", "ellipsis", "overflow", "menu"]}, {"name": "three-dots", "title": "Three dots", "categories": ["Controls"], "tags": ["meatballs", "more", "ellipsis", "overflow", "menu"]}, {"name": "thunderbolt-fill", "title": "Thunderbolt fill", "categories": ["Devices"], "tags": ["plug", "port"]}, {"name": "thunderbolt", "title": "Thunderbolt", "categories": ["Devices"], "tags": ["plug", "port"]}, {"name": "ticket-detailed-fill", "title": "Ticket detailed fill", "categories": ["Real World"], "tags": ["tickets", "admission"]}, {"name": "ticket-detailed", "title": "Ticket detailed", "categories": ["Real World"], "tags": ["tickets", "admission"]}, {"name": "ticket-fill", "title": "Ticket fill", "categories": ["Real World"], "tags": ["tickets", "admission"]}, {"name": "ticket-perforated-fill", "title": "Ticket perforated fill", "categories": ["Real World"], "tags": ["tickets", "admission"]}, {"name": "ticket-perforated", "title": "Ticket perforated", "categories": ["Real World"], "tags": ["tickets", "admission"]}, {"name": "ticket", "title": "Ticket", "categories": ["Real World"], "tags": ["tickets", "admission"]}, {"name": "tiktok", "title": "Tiktok", "categories": ["Brand"], "tags": ["social"]}, {"name": "toggle-off", "title": "Toggle off", "categories": ["Controls"], "tags": ["toggle", "switch", "checkbox"]}, {"name": "toggle-on", "title": "Toggle on", "categories": ["Controls"], "tags": ["toggle", "switch", "checkbox"]}, {"name": "toggle2-off", "title": "Toggle2 off", "categories": ["Controls"], "tags": ["toggle", "switch", "checkbox"]}, {"name": "toggle2-on", "title": "Toggle2 on", "categories": ["Controls"], "tags": ["toggle", "switch", "checkbox"]}, {"name": "toggles", "title": "Toggles", "categories": ["Controls"], "tags": ["toggle", "switch", "checkbox"]}, {"name": "toggles2", "title": "Toggles2", "categories": ["Controls"], "tags": ["toggle", "switch", "checkbox"]}, {"name": "tools", "title": "Tools", "categories": ["Tools"], "tags": ["tool", "wrench", "screwdriver"]}, {"name": "tornado", "title": "Tornado", "categories": ["Weather"], "tags": ["wind"]}, {"name": "train-freight-front-fill", "title": "Train freight front fill", "categories": ["Transportation"], "tags": ["transit", "public", "rail"]}, {"name": "train-freight-front", "title": "Train freight front", "categories": ["Transportation"], "tags": ["transit", "public", "rail"]}, {"name": "train-front-fill", "title": "Train front fill", "categories": ["Transportation"], "tags": ["transit", "public", "rail"]}, {"name": "train-front", "title": "Train front", "categories": ["Transportation"], "tags": ["transit", "public", "rail"]}, {"name": "train-lightrail-front-fill", "title": "Train lightrail front fill", "categories": ["Transportation"], "tags": ["transit", "public", "rail"]}, {"name": "train-lightrail-front", "title": "Train lightrail front", "categories": ["Transportation"], "tags": ["transit", "public", "rail"]}, {"name": "translate", "title": "Translate", "categories": ["Communications"], "tags": ["translation", "languages", "language"]}, {"name": "transparency", "title": "Transparency", "categories": ["Graphics"], "tags": ["photo", "editing", "image", "picture"]}, {"name": "trash-fill", "title": "Trash fill", "categories": ["UI and keyboard"], "tags": ["trash-can", "garbage", "delete", "remove"]}, {"name": "trash", "title": "Trash", "categories": ["UI and keyboard"], "tags": ["trash-can", "garbage", "delete", "remove"]}, {"name": "trash2-fill", "title": "Trash2 fill", "categories": ["UI and keyboard"], "tags": ["trash-can", "garbage", "delete", "remove"]}, {"name": "trash2", "title": "Trash2", "categories": ["UI and keyboard"], "tags": ["trash-can", "garbage", "delete", "remove"]}, {"name": "trash3-fill", "title": "Trash3 fill", "categories": ["UI and keyboard"], "tags": ["trash-can", "garbage", "delete", "remove"]}, {"name": "trash3", "title": "Trash3", "categories": ["UI and keyboard"], "tags": ["trash-can", "garbage", "delete", "remove"]}, {"name": "tree-fill", "title": "Tree fill", "categories": ["Real world"], "tags": ["tree", "forrest"]}, {"name": "tree", "title": "Tree", "categories": ["Real world"], "tags": ["tree", "forrest"]}, {"name": "trello", "title": "Trello", "categories": ["Brand"], "tags": ["kanban", "board"]}, {"name": "triangle-fill", "title": "Triangle fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape"]}, {"name": "triangle-half", "title": "Triangle half fill", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape"]}, {"name": "triangle", "title": "Triangle", "categories": ["<PERSON><PERSON><PERSON>"], "tags": ["shape"]}, {"name": "trophy-fill", "title": "Trophy fill", "categories": ["Real world"], "tags": ["prize", "winning"]}, {"name": "trophy", "title": "Trophy", "categories": ["Real world"], "tags": ["prize", "winning"]}, {"name": "tropical-storm", "title": "Tropical storm", "categories": ["Weather"], "tags": ["hurricane"]}, {"name": "truck-flatbed", "title": "Truck flatbed", "categories": ["Commerce"], "tags": ["trucking", "shipping", "shipment", "transport", "deliver", "delivery", "vehicle"]}, {"name": "truck-front-fill", "title": "Truck front fill", "categories": ["Transportation"], "tags": ["vehicle", "shipping", "transit", "haul", "freight"]}, {"name": "truck-front", "title": "Truck front", "categories": ["Transportation"], "tags": ["vehicle", "shipping", "transit", "haul", "freight"]}, {"name": "truck", "title": "Truck", "categories": ["Commerce"], "tags": ["trucking", "shipping", "shipment", "transport", "deliver", "delivery", "vehicle"]}, {"name": "tsunami", "title": "<PERSON><PERSON><PERSON>", "categories": ["Weather"], "tags": ["wave"]}, {"name": "tv-fill", "title": "TV fill", "categories": ["Devices"], "tags": ["television", "monitor", "display"]}, {"name": "tv", "title": "TV", "categories": ["Devices"], "tags": ["television", "monitor", "display"]}, {"name": "twitch", "title": "Twitch", "categories": ["Brand"], "tags": ["social"]}, {"name": "twitter-x", "title": "Twitter X", "categories": ["Brand"], "tags": ["social", "chat"]}, {"name": "twitter", "title": "Twitter", "categories": ["Brand"], "tags": ["social", "chat"]}, {"name": "type-bold", "title": "Type bold", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "type-h1", "title": "Type H1", "categories": ["Typography"], "tags": ["text", "type", "heading"]}, {"name": "type-h2", "title": "Type H2", "categories": ["Typography"], "tags": ["text", "type", "heading"]}, {"name": "type-h3", "title": "Type H3", "categories": ["Typography"], "tags": ["text", "type", "heading"]}, {"name": "type-h4", "title": "Type h4", "categories": ["Typography"], "tags": ["text", "type", "heading"]}, {"name": "type-h5", "title": "Type h5", "categories": ["Typography"], "tags": ["text", "type", "heading"]}, {"name": "type-h6", "title": "Type h6", "categories": ["Typography"], "tags": ["text", "type", "heading"]}, {"name": "type-italic", "title": "Type italic", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "type-strikethrough", "title": "Type strikethrough", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "type-underline", "title": "Type underline", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "type", "title": "Type", "categories": ["Typography"], "tags": ["text", "type"]}, {"name": "ubuntu", "title": "Ubuntu", "categories": ["Brand"], "tags": ["linux", "distro"]}, {"name": "ui-checks-grid", "title": "UI checks grid", "categories": ["Controls"], "tags": ["checkbox", "form"]}, {"name": "ui-checks", "title": "UI checks", "categories": ["Controls"], "tags": ["checkbox", "form"]}, {"name": "ui-radios-grid", "title": "UI radios grid", "categories": ["Controls"], "tags": ["radio", "form"]}, {"name": "ui-radios", "title": "Ui radios", "categories": ["Controls"], "tags": ["radio", "form"]}, {"name": "umbrella-fill", "title": "Umbrella fill", "categories": ["Weather"], "tags": ["rain"]}, {"name": "umbrella", "title": "Um<PERSON>lla", "categories": ["Weather"], "tags": ["rain"]}, {"name": "unindent", "title": "Unindent", "categories": ["UI and Keyboard"], "tags": ["tab", "indent"]}, {"name": "union", "title": "Union", "categories": ["Graphics"], "tags": ["graphics", "vector", "merge", "layers"]}, {"name": "unity", "title": "Unity", "categories": ["Brand"], "tags": ["gaming"]}, {"name": "universal-access-circle", "title": "Universal access circle", "categories": ["UI and Keyboard"], "tags": ["accessibility", "a11y"]}, {"name": "universal-access", "title": "Universal Access", "categories": ["UI and Keyboard"], "tags": ["accessibility", "a11y"]}, {"name": "unlock-fill", "title": "Unlock fill", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "unlock", "title": "Unlock", "categories": ["Security"], "tags": ["privacy", "security"]}, {"name": "upc-scan", "title": "Upc scan", "categories": ["Commerce"], "tags": ["purchase", "barcode"]}, {"name": "upc", "title": "Upc", "categories": ["Commerce"], "tags": ["purchase", "barcode"]}, {"name": "upload", "title": "Upload", "categories": ["Miscellaneous"], "tags": ["arrow", "network"]}, {"name": "usb-c-fill", "title": "USB C fill", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "usb-c", "title": "USB C", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "usb-drive-fill", "title": "USB drive fill", "categories": ["Devices"], "tags": ["thumb-drive"]}, {"name": "usb-drive", "title": "USB drive", "categories": ["Devices"], "tags": ["thumb-drive"]}, {"name": "usb-fill", "title": "USB fill", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "usb-micro-fill", "title": "USB micro fill", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "usb-micro", "title": "USB micro", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "usb-mini-fill", "title": "USB mini fill", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "usb-mini", "title": "USB mini", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "usb-plug-fill", "title": "USB plug fill", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "usb-plug", "title": "USB plug", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "usb-symbol", "title": "USB symbol", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "usb", "title": "USB", "categories": ["Devices"], "tags": ["port", "plug"]}, {"name": "valentine", "title": "<PERSON>", "categories": ["Love"], "tags": ["love", "romance", "valentine"]}, {"name": "valentine2", "title": "Valentine2", "categories": ["Love"], "tags": ["love", "romance", "valentine"]}, {"name": "vector-pen", "title": "Vector pen", "categories": ["Graphics"], "tags": ["graphics", "vector", "pen"]}, {"name": "view-list", "title": "View list", "categories": ["UI and keyboard"], "tags": ["view", "rearrange"]}, {"name": "view-stacked", "title": "View stacked", "categories": ["UI and keyboard"], "tags": ["view", "rearrange"]}, {"name": "vignette", "title": "Vignette", "categories": ["Graphics"], "tags": ["photo", "editing", "image", "picture"]}, {"name": "vimeo", "title": "Vimeo", "categories": ["Brand"], "tags": ["social"]}, {"name": "vinyl-fill", "title": "Vinyl fill", "categories": ["Media"], "tags": ["audio", "music", "record"]}, {"name": "vinyl", "title": "Vinyl", "categories": ["Media"], "tags": ["audio", "music", "record"]}, {"name": "virus", "title": "Virus", "categories": ["Medical"], "tags": ["covid", "viral"]}, {"name": "virus2", "title": "Virus2", "categories": ["Medical"], "tags": ["covid", "viral"]}, {"name": "voicemail", "title": "Voicemail", "categories": ["Communications"], "tags": ["voicemail", "message", "telephone"]}, {"name": "volume-down-fill", "title": "Volume down fill", "categories": ["Media"], "tags": ["audio", "video", "av", "sound"]}, {"name": "volume-down", "title": "Volume down", "categories": ["Media"], "tags": ["audio", "video", "av", "sound"]}, {"name": "volume-mute-fill", "title": "Volume mute fill", "categories": ["Media"], "tags": ["audio", "video", "av", "sound"]}, {"name": "volume-mute", "title": "Volume mute", "categories": ["Media"], "tags": ["audio", "video", "av", "sound"]}, {"name": "volume-off-fill", "title": "Volume off fill", "categories": ["Media"], "tags": ["audio", "video", "av", "sound"]}, {"name": "volume-off", "title": "Volume off", "categories": ["Media"], "tags": ["audio", "video", "av", "sound"]}, {"name": "volume-up-fill", "title": "Volume up fill", "categories": ["Media"], "tags": ["audio", "video", "av", "sound"]}, {"name": "volume-up", "title": "Volume up", "categories": ["Media"], "tags": ["audio", "video", "av", "sound"]}, {"name": "vr", "title": "VR", "categories": ["Typography"], "tags": ["divider", "vertical-rule"]}, {"name": "wallet-fill", "title": "Wallet fill", "categories": ["Commerce"], "tags": ["cards", "money", "funds", "cash", "payment"]}, {"name": "wallet", "title": "Wallet", "categories": ["Commerce"], "tags": ["cards", "money", "funds", "cash", "payment"]}, {"name": "wallet2", "title": "Wallet2", "categories": ["Commerce"], "tags": ["cards", "money", "funds", "cash", "payment"]}, {"name": "watch", "title": "Watch", "categories": ["Devices"], "tags": ["wearables", "clock"]}, {"name": "water", "title": "Water", "categories": ["Weather"], "tags": ["waves", "levels"]}, {"name": "webcam-fill", "title": "Webcam fill", "categories": ["Devices"], "tags": ["camera", "video"]}, {"name": "webcam", "title": "Webcam", "categories": ["Devices"], "tags": ["camera", "video"]}, {"name": "wechat", "title": "Wechat", "categories": ["Brand"], "tags": ["social", "messaging"]}, {"name": "whatsapp", "title": "Whatsapp", "categories": ["Brand"], "tags": ["social", "facebook", "chat"]}, {"name": "wifi-1", "title": "Wifi 1", "categories": ["Communications"], "tags": ["internet", "network", "wireless"]}, {"name": "wifi-2", "title": "Wifi 2", "categories": ["Communications"], "tags": ["internet", "network", "wireless"]}, {"name": "wifi-off", "title": "Wifi off", "categories": ["Communications"], "tags": ["internet", "network", "wireless"]}, {"name": "wifi", "title": "Wifi", "categories": ["Communications"], "tags": ["internet", "network", "wireless"]}, {"name": "wikipedia", "title": "Wikipedia", "categories": ["Brand"], "tags": ["encyclopedia"]}, {"name": "wind", "title": "Wind", "categories": ["Weather"], "tags": ["windy", "breeze"]}, {"name": "window-dash", "title": "Window dash", "categories": ["Apps"], "tags": ["application", "desktop", "app"]}, {"name": "window-desktop", "title": "Window desktop", "categories": ["Apps"], "tags": ["application", "desktop", "app"]}, {"name": "window-dock", "title": "Window dock", "categories": ["Apps"], "tags": ["application", "desktop", "os"]}, {"name": "window-fullscreen", "title": "Window fullscreen", "categories": ["Apps"], "tags": ["application", "desktop", "app"]}, {"name": "window-plus", "title": "Window plus", "categories": ["Apps"], "tags": ["application", "desktop", "app"]}, {"name": "window-sidebar", "title": "Window sidebar", "categories": ["Apps"], "tags": ["application", "desktop"]}, {"name": "window-split", "title": "Window split", "categories": ["Apps"], "tags": ["application", "desktop", "app"]}, {"name": "window-stack", "title": "Window stack", "categories": ["Apps"], "tags": ["application", "desktop", "app", "windows"]}, {"name": "window-x", "title": "Window x", "categories": ["Apps"], "tags": ["application", "desktop", "app"]}, {"name": "window", "title": "Window", "categories": ["Layout"], "tags": ["app", "application"]}, {"name": "windows", "title": "Windows", "categories": ["Brand"], "tags": ["microsoft", "msft", "windows"]}, {"name": "wordpress", "title": "Wordpress", "categories": ["Brand"], "tags": ["social"]}, {"name": "wrench-adjustable-circle-fill", "title": "Wrench adjustable circle fill", "categories": ["Tools"], "tags": ["tool"]}, {"name": "wrench-adjustable-circle", "title": "Wrench adjustable circle", "categories": ["Tools"], "tags": ["tool"]}, {"name": "wrench-adjustable", "title": "Wrench adjustable", "categories": ["Tools"], "tags": ["tool"]}, {"name": "wrench", "title": "<PERSON><PERSON>", "categories": ["Tools"], "tags": ["tool"]}, {"name": "x-circle-fill", "title": "X circle fill", "categories": ["Alerts, warnings, and signs"], "tags": ["x", "delete", "remove", "reset", "clear", "cancel", "close", "exit"]}, {"name": "x-circle", "title": "X circle", "categories": ["Alerts, warnings, and signs"], "tags": ["x", "delete", "remove", "reset", "clear", "cancel", "close", "exit"]}, {"name": "x-diamond-fill", "title": "X diamond fill", "categories": ["Alerts, warnings, and signs"], "tags": ["x", "delete", "reset", "clear", "cancel", "close", "exit"]}, {"name": "x-diamond", "title": "X diamond", "categories": ["Alerts, warnings, and signs"], "tags": ["x", "delete", "reset", "clear", "cancel", "close", "exit"]}, {"name": "x-lg", "title": "X lg", "categories": ["Alerts, warnings, and signs"], "tags": ["x", "delete", "remove", "reset", "clear", "cancel", "close", "exit"]}, {"name": "x-octagon-fill", "title": "X octagon fill", "categories": ["Alerts, warnings, and signs"], "tags": ["x", "delete", "remove", "reset", "clear", "cancel", "close", "exit"]}, {"name": "x-octagon", "title": "X octagon", "categories": ["Alerts, warnings, and signs"], "tags": ["x", "delete", "remove", "reset", "clear", "cancel", "close", "exit"]}, {"name": "x-square-fill", "title": "X square fill", "categories": ["Alerts, warnings, and signs"], "tags": ["x", "delete", "remove", "reset", "clear", "cancel", "close", "exit"]}, {"name": "x-square", "title": "X square", "categories": ["Alerts, warnings, and signs"], "tags": ["x", "delete", "remove", "reset", "clear", "cancel", "close", "exit"]}, {"name": "x", "title": "X", "categories": ["Alerts, warnings, and signs"], "tags": ["x", "delete", "remove", "reset", "clear", "cancel", "close", "exit"]}, {"name": "xbox", "title": "Xbox", "categories": ["Brand"], "tags": ["microsoft", "gaming"]}, {"name": "yelp", "title": "Yelp", "categories": ["Brand"], "tags": ["social", "reviews"]}, {"name": "yin-yang", "title": "<PERSON> yang", "categories": ["Real World"], "tags": ["peace"]}, {"name": "youtube", "title": "Youtube", "categories": ["Brand"], "tags": ["social", "video", "google"]}, {"name": "zoom-in", "title": "Zoom in", "categories": ["Graphics"], "tags": ["magnify", "scale"]}, {"name": "zoom-out", "title": "Zoom out", "categories": ["Graphics"], "tags": ["minify", "scale"]}]