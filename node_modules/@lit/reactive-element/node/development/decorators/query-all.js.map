{"version": 3, "file": "query-all.js", "sources": ["../../../src/decorators/query-all.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport type {ReactiveElement} from '../reactive-element.js';\nimport {desc, type Interface} from './base.js';\n\nexport type QueryAllDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: <PERSON><PERSON><PERSON>,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends NodeList>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n// Shared fragment used to generate empty NodeLists when a render root is\n// undefined\nlet fragment: DocumentFragment;\n\n/**\n * A property decorator that converts a class property into a getter\n * that executes a querySelectorAll on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See:\n * https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelectorAll\n *\n * ```ts\n * class MyElement {\n *   @queryAll('div')\n *   divs: NodeListOf<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function queryAll(selector: string): QueryAllDecorator {\n  return ((\n    obj: object,\n    name: PropertyKey | ClassAccessorDecoratorContext<unknown, unknown>\n  ) => {\n    return desc(obj, name, {\n      get(this: ReactiveElement) {\n        const container =\n          this.renderRoot ?? (fragment ??= document.createDocumentFragment());\n        return container.querySelectorAll(selector);\n      },\n    });\n  }) as QueryAllDecorator;\n}\n"], "names": [], "mappings": ";;AAAA;;;;AAIG;AA4BH;AACA;AACA,IAAI,QAA0B,CAAC;AAE/B;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACG,SAAU,QAAQ,CAAC,QAAgB,EAAA;AACvC,IAAA,QAAQ,CACN,GAAW,EACX,IAAmE,KACjE;AACF,QAAA,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE;YACrB,GAAG,GAAA;AACD,gBAAA,MAAM,SAAS,GACb,IAAI,CAAC,UAAU,KAAK,QAAQ,KAAK,QAAQ,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACtE,gBAAA,OAAO,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;aAC7C;AACF,SAAA,CAAC,CAAC;AACL,KAAC,EAAuB;AAC1B;;;;"}