{"version": 3, "file": "state.js", "sources": ["../../src/decorators/state.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {property} from './property.js';\n\nexport interface StateDeclaration<Type = unknown> {\n  /**\n   * A function that indicates if a property should be considered changed when\n   * it is set. The function should take the `newValue` and `oldValue` and\n   * return `true` if an update should be requested.\n   */\n  hasChanged?(value: Type, oldValue: Type): boolean;\n}\n\n/**\n * @deprecated use StateDeclaration\n */\nexport type InternalPropertyDeclaration<Type = unknown> =\n  StateDeclaration<Type>;\n\n/**\n * Declares a private or protected reactive property that still triggers\n * updates to the element when it changes. It does not reflect from the\n * corresponding attribute.\n *\n * Properties declared this way must not be used from HTML or HTML templating\n * systems, they're solely for properties internal to the element. These\n * properties may be renamed by optimization tools like closure compiler.\n * @category Decorator\n */\nexport function state(options?: StateDeclaration) {\n  return property({\n    ...options,\n    // Add both `state` and `attribute` because we found a third party\n    // controller that is keying off of PropertyOptions.state to determine\n    // whether a field is a private internal property or not.\n    state: true,\n    attribute: false,\n  });\n}\n"], "names": ["state", "options", "property", "attribute"], "mappings": ";;;;;GAwCM,SAAUA,EAAMC,GACpB,OAAOC,EAAS,IACXD,EAIHD,OAAO,EACPG,WAAW,GAEf"}