{"version": 3, "file": "custom-element.js", "sources": ["../../src/decorators/custom-element.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport type {Constructor} from './base.js';\n\n/**\n * Allow for custom element classes with private constructors\n */\ntype CustomElementClass = Omit<typeof HTMLElement, 'new'>;\n\nexport type CustomElementDecorator = {\n  // legacy\n  (cls: CustomElementClass): void;\n\n  // standard\n  (\n    target: CustomElementClass,\n    context: ClassDecoratorContext<Constructor<HTMLElement>>\n  ): void;\n};\n\n/**\n * Class decorator factory that defines the decorated class as a custom element.\n *\n * ```js\n * @customElement('my-element')\n * class MyElement extends LitElement {\n *   render() {\n *     return html``;\n *   }\n * }\n * ```\n * @category Decorator\n * @param tagName The tag name of the custom element to define.\n */\nexport const customElement =\n  (tagName: string): CustomElementDecorator =>\n  (\n    classOrTarget: CustomElementClass | Constructor<HTMLElement>,\n    context?: ClassDecoratorContext<Constructor<HTMLElement>>\n  ) => {\n    if (context !== undefined) {\n      context.addInitializer(() => {\n        customElements.define(\n          tagName,\n          classOrTarget as CustomElementConstructor\n        );\n      });\n    } else {\n      customElements.define(tagName, classOrTarget as CustomElementConstructor);\n    }\n  };\n"], "names": ["customElement", "tagName", "classOrTarget", "context", "undefined", "addInitializer", "customElements", "define"], "mappings": ";;;;;AA6CO,MAAMA,EACVC,GACD,CACEC,EACAC,UAEgBC,IAAZD,EACFA,EAAQE,gBAAe,KACrBC,eAAeC,OACbN,EACAC,EACD,IAGHI,eAAeC,OAAON,EAASC,EAChC"}