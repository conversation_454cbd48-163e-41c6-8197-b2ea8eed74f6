{"version": 3, "file": "css-tag.js", "sources": ["../src/css-tag.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nconst NODE_MODE = false;\n\n// Allows minifiers to rename references to globalThis\nconst global = globalThis;\n\n/**\n * Whether the current browser supports `adoptedStyleSheets`.\n */\nexport const supportsAdoptingStyleSheets: boolean =\n  global.ShadowRoot &&\n  (global.ShadyCSS === undefined || global.ShadyCSS.nativeShadow) &&\n  'adoptedStyleSheets' in Document.prototype &&\n  'replace' in CSSStyleSheet.prototype;\n\n/**\n * A CSSResult or native CSSStyleSheet.\n *\n * In browsers that support constructible CSS style sheets, CSSStyleSheet\n * object can be used for styling along side CSSResult from the `css`\n * template tag.\n */\nexport type CSSResultOrNative = CSSResult | CSSStyleSheet;\n\nexport type CSSResultArray = Array<CSSResultOrNative | CSSResultArray>;\n\n/**\n * A single CSSResult, CSSStyleSheet, or an array or nested arrays of those.\n */\nexport type CSSResultGroup = CSSResultOrNative | CSSResultArray;\n\nconst constructionToken = Symbol();\n\nconst cssTagCache = new WeakMap<TemplateStringsArray, CSSStyleSheet>();\n\n/**\n * A container for a string of CSS text, that may be used to create a CSSStyleSheet.\n *\n * CSSResult is the return value of `css`-tagged template literals and\n * `unsafeCSS()`. In order to ensure that CSSResults are only created via the\n * `css` tag and `unsafeCSS()`, CSSResult cannot be constructed directly.\n */\nexport class CSSResult {\n  // This property needs to remain unminified.\n  ['_$cssResult$'] = true;\n  readonly cssText: string;\n  private _styleSheet?: CSSStyleSheet;\n  private _strings: TemplateStringsArray | undefined;\n\n  private constructor(\n    cssText: string,\n    strings: TemplateStringsArray | undefined,\n    safeToken: symbol\n  ) {\n    if (safeToken !== constructionToken) {\n      throw new Error(\n        'CSSResult is not constructable. Use `unsafeCSS` or `css` instead.'\n      );\n    }\n    this.cssText = cssText;\n    this._strings = strings;\n  }\n\n  // This is a getter so that it's lazy. In practice, this means stylesheets\n  // are not created until the first element instance is made.\n  get styleSheet(): CSSStyleSheet | undefined {\n    // If `supportsAdoptingStyleSheets` is true then we assume CSSStyleSheet is\n    // constructable.\n    let styleSheet = this._styleSheet;\n    const strings = this._strings;\n    if (supportsAdoptingStyleSheets && styleSheet === undefined) {\n      const cacheable = strings !== undefined && strings.length === 1;\n      if (cacheable) {\n        styleSheet = cssTagCache.get(strings);\n      }\n      if (styleSheet === undefined) {\n        (this._styleSheet = styleSheet = new CSSStyleSheet()).replaceSync(\n          this.cssText\n        );\n        if (cacheable) {\n          cssTagCache.set(strings, styleSheet);\n        }\n      }\n    }\n    return styleSheet;\n  }\n\n  toString(): string {\n    return this.cssText;\n  }\n}\n\ntype ConstructableCSSResult = CSSResult & {\n  new (\n    cssText: string,\n    strings: TemplateStringsArray | undefined,\n    safeToken: symbol\n  ): CSSResult;\n};\n\nconst textFromCSSResult = (value: CSSResultGroup | number) => {\n  // This property needs to remain unminified.\n  if ((value as CSSResult)['_$cssResult$'] === true) {\n    return (value as CSSResult).cssText;\n  } else if (typeof value === 'number') {\n    return value;\n  } else {\n    throw new Error(\n      `Value passed to 'css' function must be a 'css' function result: ` +\n        `${value}. Use 'unsafeCSS' to pass non-literal values, but take care ` +\n        `to ensure page security.`\n    );\n  }\n};\n\n/**\n * Wrap a value for interpolation in a {@linkcode css} tagged template literal.\n *\n * This is unsafe because untrusted CSS text can be used to phone home\n * or exfiltrate data to an attacker controlled site. Take care to only use\n * this with trusted input.\n */\nexport const unsafeCSS = (value: unknown) =>\n  new (CSSResult as ConstructableCSSResult)(\n    typeof value === 'string' ? value : String(value),\n    undefined,\n    constructionToken\n  );\n\n/**\n * A template literal tag which can be used with LitElement's\n * {@linkcode LitElement.styles} property to set element styles.\n *\n * For security reasons, only literal string values and number may be used in\n * embedded expressions. To incorporate non-literal values {@linkcode unsafeCSS}\n * may be used inside an expression.\n */\nexport const css = (\n  strings: TemplateStringsArray,\n  ...values: (CSSResultGroup | number)[]\n): CSSResult => {\n  const cssText =\n    strings.length === 1\n      ? strings[0]\n      : values.reduce(\n          (acc, v, idx) => acc + textFromCSSResult(v) + strings[idx + 1],\n          strings[0]\n        );\n  return new (CSSResult as ConstructableCSSResult)(\n    cssText,\n    strings,\n    constructionToken\n  );\n};\n\n/**\n * Applies the given styles to a `shadowRoot`. When Shadow DOM is\n * available but `adoptedStyleSheets` is not, styles are appended to the\n * `shadowRoot` to [mimic the native feature](https://developer.mozilla.org/en-US/docs/Web/API/ShadowRoot/adoptedStyleSheets).\n * Note, when shimming is used, any styles that are subsequently placed into\n * the shadowRoot should be placed *before* any shimmed adopted styles. This\n * will match spec behavior that gives adopted sheets precedence over styles in\n * shadowRoot.\n */\nexport const adoptStyles = (\n  renderRoot: ShadowRoot,\n  styles: Array<CSSResultOrNative>\n) => {\n  if (supportsAdoptingStyleSheets) {\n    (renderRoot as ShadowRoot).adoptedStyleSheets = styles.map((s) =>\n      s instanceof CSSStyleSheet ? s : s.styleSheet!\n    );\n  } else {\n    for (const s of styles) {\n      const style = document.createElement('style');\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const nonce = (global as any)['litNonce'];\n      if (nonce !== undefined) {\n        style.setAttribute('nonce', nonce);\n      }\n      style.textContent = (s as CSSResult).cssText;\n      renderRoot.appendChild(style);\n    }\n  }\n};\n\nconst cssResultFromStyleSheet = (sheet: CSSStyleSheet) => {\n  let cssText = '';\n  for (const rule of sheet.cssRules) {\n    cssText += rule.cssText;\n  }\n  return unsafeCSS(cssText);\n};\n\nexport const getCompatibleStyle =\n  supportsAdoptingStyleSheets ||\n  (NODE_MODE && global.CSSStyleSheet === undefined)\n    ? (s: CSSResultOrNative) => s\n    : (s: CSSResultOrNative) =>\n        s instanceof CSSStyleSheet ? cssResultFromStyleSheet(s) : s;\n"], "names": ["global", "globalThis", "supportsAdoptingStyleSheets", "ShadowRoot", "undefined", "ShadyCSS", "nativeShadow", "Document", "prototype", "CSSStyleSheet", "constructionToken", "Symbol", "cssTagCache", "WeakMap", "CSSResult", "constructor", "cssText", "strings", "safeToken", "this", "Error", "_strings", "styleSheet", "_styleSheet", "cacheable", "length", "get", "replaceSync", "set", "toString", "unsafeCSS", "value", "String", "css", "values", "reduce", "acc", "v", "idx", "textFromCSSResult", "adoptStyles", "renderRoot", "styles", "adoptedStyleSheets", "map", "s", "style", "document", "createElement", "nonce", "setAttribute", "textContent", "append<PERSON><PERSON><PERSON>", "getCompatibleStyle", "sheet", "rule", "cssRules", "cssResultFromStyleSheet"], "mappings": ";;;;;AASA,MAAMA,EAASC,WAKFC,EACXF,EAAOG,kBACcC,IAApBJ,EAAOK,UAA0BL,EAAOK,SAASC,eAClD,uBAAwBC,SAASC,WACjC,YAAaC,cAAcD,UAkBvBE,EAAoBC,SAEpBC,EAAc,IAAIC,cASXC,EAOX,WAAAC,CACEC,EACAC,EACAC,GAEA,GAVFC,KAAe,cAAI,EAUbD,IAAcR,EAChB,MAAUU,MACR,qEAGJD,KAAKH,QAAUA,EACfG,KAAKE,EAAWJ,CACjB,CAID,cAAIK,GAGF,IAAIA,EAAaH,KAAKI,EACtB,MAAMN,EAAUE,KAAKE,EACrB,GAAInB,QAA8CE,IAAfkB,EAA0B,CAC3D,MAAME,OAAwBpB,IAAZa,GAA4C,IAAnBA,EAAQQ,OAC/CD,IACFF,EAAaV,EAAYc,IAAIT,SAEZb,IAAfkB,KACDH,KAAKI,EAAcD,EAAa,IAAIb,eAAiBkB,YACpDR,KAAKH,SAEHQ,GACFZ,EAAYgB,IAAIX,EAASK,GAG9B,CACD,OAAOA,CACR,CAED,QAAAO,GACE,OAAOV,KAAKH,OACb,EAWH,MAsBac,EAAaC,GACxB,IAAKjB,EACc,iBAAViB,EAAqBA,EAAeA,EAAPC,QACpC5B,EACAM,GAWSuB,EAAM,CACjBhB,KACGiB,KAEH,MAAMlB,EACe,IAAnBC,EAAQQ,OACJR,EAAQ,GACRiB,EAAOC,QACL,CAACC,EAAKC,EAAGC,IAAQF,EA7CD,CAACL,IAEzB,IAA6C,IAAxCA,EAAkC,aACrC,OAAQA,EAAoBf,QACvB,GAAqB,iBAAVe,EAChB,OAAOA,EAEP,MAAUX,MACR,mEACKW,EADL,uFAIH,EAiC8BQ,CAAkBF,GAAKpB,EAAQqB,EAAM,IAC5DrB,EAAQ,IAEhB,OAAO,IAAKH,EACVE,EACAC,EACAP,EACD,EAYU8B,EAAc,CACzBC,EACAC,KAEA,GAAIxC,EACDuC,EAA0BE,mBAAqBD,EAAOE,KAAKC,GAC1DA,aAAapC,cAAgBoC,EAAIA,EAAEvB,kBAGrC,IAAK,MAAMuB,KAAKH,EAAQ,CACtB,MAAMI,EAAQC,SAASC,cAAc,SAE/BC,EAASjD,EAAyB,cAC1BI,IAAV6C,GACFH,EAAMI,aAAa,QAASD,GAE9BH,EAAMK,YAAeN,EAAgB7B,QACrCyB,EAAWW,YAAYN,EACxB,CACF,EAWUO,EACXnD,QACuCE,IAAzBJ,EAAOS,cAChBoC,GAAyBA,EACzBA,GACCA,aAAapC,cAbW,CAAC6C,IAC/B,IAAItC,EAAU,GACd,IAAK,MAAMuC,KAAQD,EAAME,SACvBxC,GAAWuC,EAAKvC,QAElB,OAAOc,EAAUd,EAAQ,EAQUyC,CAAwBZ,GAAKA"}