{"version": 3, "file": "query-assigned-elements.js", "sources": ["../src/decorators/query-assigned-elements.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport type {ReactiveElement} from '../reactive-element.js';\nimport type {QueryAssignedNodesOptions} from './query-assigned-nodes.js';\nimport {desc, type Interface} from './base.js';\n\nexport type QueryAssignedElementsDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: <PERSON><PERSON><PERSON>,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends Array<Element>>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n/**\n * Options for the {@linkcode queryAssignedElements} decorator. Extends the\n * options that can be passed into\n * [HTMLSlotElement.assignedElements](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n */\nexport interface QueryAssignedElementsOptions\n  extends QueryAssignedNodesOptions {\n  /**\n   * CSS selector used to filter the elements returned. For example, a selector\n   * of `\".item\"` will only include elements with the `item` class.\n   */\n  selector?: string;\n}\n\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedElements` of the given `slot`. Provides a declarative\n * way to use\n * [`HTMLSlotElement.assignedElements`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n *\n * Can be passed an optional {@linkcode QueryAssignedElementsOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedElements({ slot: 'list' })\n *   listItems!: Array<HTMLElement>;\n *   @queryAssignedElements()\n *   unnamedSlotEls!: Array<HTMLElement>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *       <slot></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note, the type of this property should be annotated as `Array<HTMLElement>`.\n *\n * @category Decorator\n */\nexport function queryAssignedElements(\n  options?: QueryAssignedElementsOptions\n): QueryAssignedElementsDecorator {\n  return (<V extends Array<Element>>(\n    obj: object,\n    name: PropertyKey | ClassAccessorDecoratorContext<unknown, unknown>\n  ) => {\n    const {slot, selector} = options ?? {};\n    const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n    return desc(obj, name, {\n      get(this: ReactiveElement): V {\n        const slotEl =\n          this.renderRoot?.querySelector<HTMLSlotElement>(slotSelector);\n        const elements = slotEl?.assignedElements(options) ?? [];\n        return (\n          selector === undefined\n            ? elements\n            : elements.filter((node) => node.matches(selector))\n        ) as V;\n      },\n    });\n  }) as QueryAssignedElementsDecorator;\n}\n"], "names": ["queryAssignedElements", "options", "obj", "name", "slot", "selector", "slotSelector", "desc", "get", "slotEl", "this", "renderRoot", "querySelector", "elements", "assignedElements", "undefined", "filter", "node", "matches"], "mappings": ";;;;;GA6EM,SAAUA,EACdC,GAEA,MAAQ,CACNC,EACAC,KAEA,MAAMC,KAACA,EAAIC,SAAEA,GAAYJ,GAAW,CAAA,EAC9BK,EAAe,QAAOF,EAAO,SAASA,KAAU,gBACtD,OAAOG,EAAKL,EAAKC,EAAM,CACrB,GAAAK,GACE,MAAMC,EACJC,KAAKC,YAAYC,cAA+BN,GAC5CO,EAAWJ,GAAQK,iBAAiBb,IAAY,GACtD,YACec,IAAbV,EACIQ,EACAA,EAASG,QAAQC,GAASA,EAAKC,QAAQb,IAE9C,GAEJ,CACH"}