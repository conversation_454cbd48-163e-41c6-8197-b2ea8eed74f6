{"version": 3, "file": "decorators.js", "sourceRoot": "", "sources": ["../src/decorators.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,wEAAwE;AACxE,+EAA+E;AAC/E,iCAAiC;AAEjC,cAAc,gCAAgC,CAAC;AAC/C,cAAc,0BAA0B,CAAC;AACzC,cAAc,uBAAuB,CAAC;AACtC,cAAc,+BAA+B,CAAC;AAC9C,cAAc,uBAAuB,CAAC;AACtC,cAAc,2BAA2B,CAAC;AAC1C,cAAc,6BAA6B,CAAC;AAC5C,cAAc,yCAAyC,CAAC;AACxD,cAAc,sCAAsC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// This module exports decorators that are compatible both with standard\n// decorators as implemented by TypeScript 5.2 and Babel, and with TypeScript's\n// experimentalDecorators option.\n\nexport * from './decorators/custom-element.js';\nexport * from './decorators/property.js';\nexport * from './decorators/state.js';\nexport * from './decorators/event-options.js';\nexport * from './decorators/query.js';\nexport * from './decorators/query-all.js';\nexport * from './decorators/query-async.js';\nexport * from './decorators/query-assigned-elements.js';\nexport * from './decorators/query-assigned-nodes.js';\n"]}