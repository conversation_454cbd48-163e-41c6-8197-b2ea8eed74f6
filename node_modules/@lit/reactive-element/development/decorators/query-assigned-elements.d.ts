/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
import type { ReactiveElement } from '../reactive-element.js';
import type { QueryAssignedNodesOptions } from './query-assigned-nodes.js';
import { type Interface } from './base.js';
export type QueryAssignedElementsDecorator = {
    (proto: Interface<ReactiveElement>, name: <PERSON><PERSON><PERSON>, descriptor?: PropertyDescriptor): void | any;
    <C extends Interface<ReactiveElement>, V extends Array<Element>>(value: ClassAccessorDecoratorTarget<C, V>, context: ClassAccessorDecoratorContext<C, V>): ClassAccessorDecoratorResult<C, V>;
};
/**
 * Options for the {@linkcode queryAssignedElements} decorator. Extends the
 * options that can be passed into
 * [HTMLSlotElement.assignedElements](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).
 */
export interface QueryAssignedElementsOptions extends QueryAssignedNodesOptions {
    /**
     * CSS selector used to filter the elements returned. For example, a selector
     * of `".item"` will only include elements with the `item` class.
     */
    selector?: string;
}
/**
 * A property decorator that converts a class property into a getter that
 * returns the `assignedElements` of the given `slot`. Provides a declarative
 * way to use
 * [`HTMLSlotElement.assignedElements`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).
 *
 * Can be passed an optional {@linkcode QueryAssignedElementsOptions} object.
 *
 * Example usage:
 * ```ts
 * class MyElement {
 *   @queryAssignedElements({ slot: 'list' })
 *   listItems!: Array<HTMLElement>;
 *   @queryAssignedElements()
 *   unnamedSlotEls!: Array<HTMLElement>;
 *
 *   render() {
 *     return html`
 *       <slot name="list"></slot>
 *       <slot></slot>
 *     `;
 *   }
 * }
 * ```
 *
 * Note, the type of this property should be annotated as `Array<HTMLElement>`.
 *
 * @category Decorator
 */
export declare function queryAssignedElements(options?: QueryAssignedElementsOptions): QueryAssignedElementsDecorator;
//# sourceMappingURL=query-assigned-elements.d.ts.map