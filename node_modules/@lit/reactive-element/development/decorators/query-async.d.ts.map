{"version": 3, "file": "query-async.d.ts", "sourceRoot": "", "sources": ["../../src/decorators/query-async.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AASH,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,wBAAwB,CAAC;AAC5D,OAAO,EAAO,KAAK,SAAS,EAAC,MAAM,WAAW,CAAC;AAE/C,MAAM,MAAM,mBAAmB,GAAG;IAEhC,CACE,KAAK,EAAE,SAAS,CAAC,eAAe,CAAC,EACjC,IAAI,EAAE,WAAW,EACjB,UAAU,CAAC,EAAE,kBAAkB,GAG9B,IAAI,GAAG,GAAG,CAAC;IAGd,CAAC,CAAC,SAAS,SAAS,CAAC,eAAe,CAAC,EAAE,CAAC,SAAS,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,EACtE,KAAK,EAAE,4BAA4B,CAAC,CAAC,EAAE,CAAC,CAAC,EACzC,OAAO,EAAE,6BAA6B,CAAC,CAAC,EAAE,CAAC,CAAC,GAC3C,4BAA4B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CACvC,CAAC;AAOF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,wBAAgB,UAAU,CAAC,QAAQ,EAAE,MAAM,GAWnC,mBAAmB,CAC1B"}