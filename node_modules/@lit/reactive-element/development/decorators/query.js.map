{"version": 3, "file": "query.js", "sourceRoot": "", "sources": ["../../src/decorators/query.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AASH,OAAO,EAAC,IAAI,EAAiB,MAAM,WAAW,CAAC;AAE/C,MAAM,QAAQ,GAAG,IAAI,CAAC;AAEtB,IAAI,YAAqD,CAAC;AAE1D,IAAI,QAAQ,EAAE,CAAC;IACb,uEAAuE;IACvE,cAAc;IACd,UAAU,CAAC,iBAAiB,KAAK,IAAI,GAAG,EAAE,CAAC;IAE3C;;;;OAIG;IACH,YAAY,GAAG,CAAC,IAAY,EAAE,OAAe,EAAE,EAAE;QAC/C,OAAO,IAAI,IAAI;YACb,CAAC,CAAC,4BAA4B,IAAI,wBAAwB;YAC1D,CAAC,CAAC,EAAE,CAAC;QACP,IACE,CAAC,UAAU,CAAC,iBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC;YAC3C,CAAC,UAAU,CAAC,iBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EACxC,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,UAAU,CAAC,iBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAmBD;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,UAAU,KAAK,CAAC,QAAgB,EAAE,KAAe;IACrD,OAAO,CAAC,CACN,aAAiD,EACjD,aAAgE,EAChE,UAA+B,EAC/B,EAAE;QACF,MAAM,OAAO,GAAG,CAAC,EAA8B,EAAK,EAAE;YACpD,MAAM,MAAM,GAAG,CAAC,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAM,CAAC;YACrE,IAAI,QAAQ,IAAI,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC;gBAC3D,MAAM,IAAI,GACR,OAAO,aAAa,KAAK,QAAQ;oBAC/B,CAAC,CAAC,aAAa,CAAC,IAAI;oBACpB,CAAC,CAAC,aAAa,CAAC;gBACpB,YAAY,CACV,EAAE,EACF,kBAAkB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,oBAAoB;oBAChE,0BAA0B,QAAQ,6BAA6B;oBAC/D,8DAA8D;oBAC9D,6DAA6D;oBAC7D,kEAAkE,CACrE,CAAC;YACJ,CAAC;YACD,sEAAsE;YACtE,oEAAoE;YACpE,WAAW;YACX,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;QACF,IAAI,KAAK,EAAE,CAAC;YACV,iCAAiC;YACjC,gEAAgE;YAChE,uEAAuE;YACvE,0BAA0B;YAC1B,yEAAyE;YACzE,yDAAyD;YACzD,MAAM,EAAC,GAAG,EAAE,GAAG,EAAC,GACd,OAAO,aAAa,KAAK,QAAQ;gBAC/B,CAAC,CAAC,aAAa;gBACf,CAAC,CAAC,UAAU;oBACV,CAAC,GAAG,EAAE;wBACJ,MAAM,GAAG,GAAG,QAAQ;4BAClB,CAAC,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,mBAAmB,CAAC;4BACrD,CAAC,CAAC,MAAM,EAAE,CAAC;wBAIb,OAAO;4BACL,GAAG;gCACD,OAAQ,IAAkB,CAAC,GAAG,CAAC,CAAC;4BAClC,CAAC;4BACD,GAAG,CAAC,CAAC;gCACF,IAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;4BAC/B,CAAC;yBACF,CAAC;oBACJ,CAAC,CAAC,EAAE,CAAC;YACX,OAAO,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE;gBACxC,GAAG;oBACD,IAAI,MAAM,GAAM,GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAChC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;wBACzB,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;wBACvB,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;4BACvC,GAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;wBAC1B,CAAC;oBACH,CAAC;oBACD,OAAO,MAAM,CAAC;gBAChB,CAAC;aACF,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,6DAA6D;YAC7D,2BAA2B;YAC3B,OAAO,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE;gBACxC,GAAG;oBACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;gBACvB,CAAC;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAmB,CAAC;AACvB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport type {ReactiveElement} from '../reactive-element.js';\nimport {desc, type Interface} from './base.js';\n\nconst DEV_MODE = true;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  globalThis.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += code\n      ? ` See https://lit.dev/msg/${code} for more information.`\n      : '';\n    if (\n      !globalThis.litIssuedWarnings!.has(warning) &&\n      !globalThis.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      globalThis.litIssuedWarnings!.add(warning);\n    }\n  };\n}\n\nexport type QueryDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: PropertyKey,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends Element | null>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function query(selector: string, cache?: boolean): QueryDecorator {\n  return (<C extends Interface<ReactiveElement>, V extends Element | null>(\n    protoOrTarget: ClassAccessorDecoratorTarget<C, V>,\n    nameOrContext: PropertyKey | ClassAccessorDecoratorContext<C, V>,\n    descriptor?: PropertyDescriptor\n  ) => {\n    const doQuery = (el: Interface<ReactiveElement>): V => {\n      const result = (el.renderRoot?.querySelector(selector) ?? null) as V;\n      if (DEV_MODE && result === null && cache && !el.hasUpdated) {\n        const name =\n          typeof nameOrContext === 'object'\n            ? nameOrContext.name\n            : nameOrContext;\n        issueWarning(\n          '',\n          `@query'd field ${JSON.stringify(String(name))} with the 'cache' ` +\n            `flag set for selector '${selector}' has been accessed before ` +\n            `the first update and returned null. This is expected if the ` +\n            `renderRoot tree has not been provided beforehand (e.g. via ` +\n            `Declarative Shadow DOM). Therefore the value hasn't been cached.`\n        );\n      }\n      // TODO: if we want to allow users to assert that the query will never\n      // return null, we need a new option and to throw here if the result\n      // is null.\n      return result;\n    };\n    if (cache) {\n      // Accessors to wrap from either:\n      //   1. The decorator target, in the case of standard decorators\n      //   2. The property descriptor, in the case of experimental decorators\n      //      on auto-accessors.\n      //   3. Functions that access our own cache-key property on the instance,\n      //      in the case of experimental decorators on fields.\n      const {get, set} =\n        typeof nameOrContext === 'object'\n          ? protoOrTarget\n          : descriptor ??\n            (() => {\n              const key = DEV_MODE\n                ? Symbol(`${String(nameOrContext)} (@query() cache)`)\n                : Symbol();\n              type WithCache = ReactiveElement & {\n                [key: symbol]: Element | null;\n              };\n              return {\n                get() {\n                  return (this as WithCache)[key];\n                },\n                set(v) {\n                  (this as WithCache)[key] = v;\n                },\n              };\n            })();\n      return desc(protoOrTarget, nameOrContext, {\n        get(this: ReactiveElement): V {\n          let result: V = get!.call(this);\n          if (result === undefined) {\n            result = doQuery(this);\n            if (result !== null || this.hasUpdated) {\n              set!.call(this, result);\n            }\n          }\n          return result;\n        },\n      });\n    } else {\n      // This object works as the return type for both standard and\n      // experimental decorators.\n      return desc(protoOrTarget, nameOrContext, {\n        get(this: ReactiveElement) {\n          return doQuery(this);\n        },\n      });\n    }\n  }) as QueryDecorator;\n}\n"]}