{"version": 3, "file": "reactive-element.d.ts", "sourceRoot": "", "sources": ["../src/reactive-element.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;GAIG;AAEH,OAAO,EAGL,cAAc,EACd,iBAAiB,EAClB,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,EACV,kBAAkB,EAClB,sBAAsB,EACvB,MAAM,0BAA0B,CAAC;AAKlC,cAAc,cAAc,CAAC;AAC7B,YAAY,EACV,kBAAkB,EAClB,sBAAsB,GACvB,MAAM,0BAA0B,CAAC;AAyFlC;;;;;GAKG;AAEH,yBAAiB,gBAAgB,CAAC;IAChC;;;;;;;;OAQG;IAEH,UAAiB,QAAQ,CAAC;QACxB,KAAY,KAAK,GAAG,MAAM,CAAC;QAC3B,UAAiB,MAAM;YACrB,IAAI,EAAE,QAAQ,CAAC;SAChB;KACF;CACF;AAyCD;;GAEG;AACH,MAAM,WAAW,yBAAyB,CAAC,IAAI,GAAG,OAAO,EAAE,QAAQ,GAAG,OAAO;IAC3E;;;OAGG;IACH,aAAa,CAAC,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC;IAE5D;;;;;;OAMG;IACH,WAAW,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC;CACrD;AAED,KAAK,kBAAkB,CAAC,IAAI,GAAG,OAAO,EAAE,QAAQ,GAAG,OAAO,IACtD,yBAAyB,CAAC,IAAI,CAAC,GAC/B,CAAC,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,KAAK,IAAI,CAAC,CAAC;AAEtD;;GAEG;AACH,MAAM,WAAW,mBAAmB,CAAC,IAAI,GAAG,OAAO,EAAE,QAAQ,GAAG,OAAO;IACrE;;;;;;OAMG;IACH,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;IAEzB;;;;;;OAMG;IACH,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC;IAEtC;;;;OAIG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC;IAEzB;;;;;;;;;;;OAWG;IACH,QAAQ,CAAC,SAAS,CAAC,EAAE,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAExD;;;;;;OAMG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;IAE3B;;;;OAIG;IACH,UAAU,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,GAAG,OAAO,CAAC;IAElD;;;;;;;OAOG;IACH,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;IAU9B;;;;;;;;;;;;;;;;OAgBG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;CACtB;AAED;;;;GAIG;AACH,MAAM,WAAW,oBAAoB;IACnC,QAAQ,EAAE,GAAG,EAAE,MAAM,GAAG,mBAAmB,CAAC;CAC7C;AAED,KAAK,sBAAsB,GAAG,GAAG,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;AAIpE;;;;;;;;;;GAUG;AAQH,MAAM,MAAM,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,MAAM,GAClD,gBAAgB,CAAC,CAAC,CAAC,GACnB,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAE9B;;GAEG;AAGH,MAAM,WAAW,gBAAgB,CAAC,CAAC,CAAE,SAAQ,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC;IACpE,GAAG,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;IAC/C,GAAG,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAClD,GAAG,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;IACtC,MAAM,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;CAC1C;AAED,eAAO,MAAM,gBAAgB,EAAE,yBAwC9B,CAAC;AAEF,MAAM,WAAW,UAAU;IACzB,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,GAAG,OAAO,CAAC;CACzC;AAED;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE,UACN,CAAC;AAWlB;;GAEG;AACH,MAAM,MAAM,WAAW,GACnB,kBAAkB,GAClB,WAAW,GACX,sBAAsB,CAAC;AAE3B,MAAM,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,eAAe,KAAK,IAAI,CAAC;AAG7D,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,iBAAiB;QACzB,QAAQ,CAAC,QAAQ,EAAE,OAAO,MAAM,CAAC;KAClC;CACF;AAMD,OAAO,CAAC,MAAM,CAAC;IAGb,IAAI,mBAAmB,EAAE,OAAO,CAC9B,MAAM,EACN,GAAG,CAAC,WAAW,EAAE,mBAAmB,CAAC,CACtC,CAAC;CACH;AAUD;;;;;GAKG;AACH,8BAAsB,eASpB,SAAQ,WACR,YAAW,sBAAsB;IAGjC;;;;;;;OAOG;IACH,MAAM,CAAC,eAAe,CAAC,EAAE,WAAW,EAAE,CAAC;IAEvC;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,KAAK,IAAI,CAAC;IAE1D;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,KAAK,IAAI,CAAC;IAE3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW;IAK9C,MAAM,CAAC,aAAa,CAAC,EAAE,WAAW,EAAE,CAAC;IAQrC;;;;;OAKG;IACH,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAe;IAEtD;;;;;OAKG;IACH,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,GAAG,SAAS,CAAC;IAE7C;;;;;;OAMG;IACH,MAAM,CAAC,iBAAiB,EAAE,sBAAsB,CAAC;IAEjD;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,MAAM,CAAC,UAAU,EAAE,oBAAoB,CAAC;IAExC;;;;;OAKG;IACH,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAM;IAEpD;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,MAAM,CAAC,MAAM,CAAC,EAAE,cAAc,CAAC;IAE/B;;;;OAIG;IACH,MAAM,KAAK,kBAAkB,aAU5B;IAED,OAAO,CAAC,oBAAoB,CAAC,CAA6B;IAE1D;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,MAAM,CAAC,cAAc,CACnB,IAAI,EAAE,WAAW,EACjB,OAAO,GAAE,mBAAgD;IA2B3D;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,SAAS,CAAC,MAAM,CAAC,qBAAqB,CACpC,IAAI,EAAE,WAAW,EACjB,GAAG,EAAE,MAAM,GAAG,MAAM,EACpB,OAAO,EAAE,mBAAmB,GAC3B,kBAAkB,GAAG,SAAS;IAsCjC;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,WAAW;IAK3C,OAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAExE;;;;;;;;;;OAUG;IACH,OAAO,CAAC,MAAM,CAAC,SAAS;IAqBxB;;;;;;;;;;OAUG;IACH,SAAS,CAAC,MAAM,CAAC,QAAQ;IA2DzB;;;;;;;;;OASG;IACH,MAAM,CAAC,iBAAiB,EAAE,cAAc,CAAkB;IAE1D;;;;;;;;;;;;;OAaG;IACH,SAAS,CAAC,MAAM,CAAC,cAAc,CAC7B,MAAM,CAAC,EAAE,cAAc,GACtB,KAAK,CAAC,iBAAiB,CAAC;IAiB3B;;;;OAIG;IACH,QAAQ,CAAC,UAAU,EAAG,WAAW,GAAG,gBAAgB,CAAC;IAErD;;;OAGG;IACH,OAAO,CAAC,MAAM,CAAC,0BAA0B;IAgBzC,OAAO,CAAC,eAAe,CAAoB;IAE3C;;;;OAIG;IACH,eAAe,UAAS;IAExB;;;;OAIG;IACH,UAAU,UAAS;IAUnB;;;OAGG;IACH,OAAO,CAAC,eAAe,CAAC,CAA4B;IAEpD;;OAEG;IACH,OAAO,CAAC,sBAAsB,CAAC,CAAmB;IAElD;;OAEG;IACH,OAAO,CAAC,oBAAoB,CAA4B;IAExD;;OAEG;IACH,OAAO,CAAC,aAAa,CAAC,CAA0B;;IAOhD;;;OAGG;IACH,OAAO,CAAC,YAAY;IAgBpB;;;;;;;;OAQG;IACH,aAAa,CAAC,UAAU,EAAE,kBAAkB;IAW5C;;;OAGG;IACH,gBAAgB,CAAC,UAAU,EAAE,kBAAkB;IAI/C;;;;;OAKG;IACH,OAAO,CAAC,wBAAwB;IAehC;;;;;;;;OAQG;IACH,SAAS,CAAC,gBAAgB,IAAI,WAAW,GAAG,gBAAgB;IAa5D;;;;OAIG;IACH,iBAAiB;IAQjB;;;;;OAKG;IACH,SAAS,CAAC,cAAc,CAAC,gBAAgB,EAAE,OAAO;IAElD;;;;;OAKG;IACH,oBAAoB;IAIpB;;;;;;;;;;;OAWG;IACH,wBAAwB,CACtB,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,MAAM,GAAG,IAAI,EACnB,KAAK,EAAE,MAAM,GAAG,IAAI;IAKtB,OAAO,CAAC,qBAAqB;IA6E7B;;;;;;;;;;;;;OAaG;IACH,aAAa,CACX,IAAI,CAAC,EAAE,WAAW,EAClB,QAAQ,CAAC,EAAE,OAAO,EAClB,OAAO,CAAC,EAAE,mBAAmB,GAC5B,IAAI;IA6EP;;OAEG;YACW,eAAe;IAuB7B;;;;;;;;;;;;;;;;OAgBG;IACH,SAAS,CAAC,cAAc,IAAI,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC;IAoBnD;;;;;;;;;OASG;IACH,SAAS,CAAC,aAAa,IAAI,IAAI;IA6F/B;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,SAAS,CAAC,UAAU,CAAC,kBAAkB,EAAE,cAAc,GAAG,IAAI;IA6B9D,OAAO,CAAC,aAAa;IAKrB;;;;;;;;;;;;;;;OAeG;IACH,IAAI,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC,CAErC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,SAAS,CAAC,iBAAiB,IAAI,OAAO,CAAC,OAAO,CAAC;IAI/C;;;;;;;OAOG;IACH,SAAS,CAAC,YAAY,CAAC,kBAAkB,EAAE,cAAc,GAAG,OAAO;IAInE;;;;;;;;OAQG;IACH,SAAS,CAAC,MAAM,CAAC,kBAAkB,EAAE,cAAc;IAUnD;;;;;;;;;OASG;IACH,SAAS,CAAC,OAAO,CAAC,kBAAkB,EAAE,cAAc;IAEpD;;;;;;;;;;;;;;;OAeG;IACH,SAAS,CAAC,YAAY,CAAC,kBAAkB,EAAE,cAAc;CAC1D"}