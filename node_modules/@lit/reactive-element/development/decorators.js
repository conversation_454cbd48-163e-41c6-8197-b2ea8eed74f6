/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
// This module exports decorators that are compatible both with standard
// decorators as implemented by TypeScript 5.2 and Babel, and with TypeScript's
// experimentalDecorators option.
export * from './decorators/custom-element.js';
export * from './decorators/property.js';
export * from './decorators/state.js';
export * from './decorators/event-options.js';
export * from './decorators/query.js';
export * from './decorators/query-all.js';
export * from './decorators/query-async.js';
export * from './decorators/query-assigned-elements.js';
export * from './decorators/query-assigned-nodes.js';
//# sourceMappingURL=decorators.js.map