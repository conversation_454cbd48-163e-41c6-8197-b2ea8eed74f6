{"name": "@lit/react", "version": "1.0.7", "description": "A React component wrapper for web components.", "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://lit.dev/", "repository": {"type": "git", "url": "https://github.com/lit/lit.git", "directory": "packages/react"}, "type": "module", "main": "index.js", "module": "index.js", "typings": "index.d.ts", "directories": {"test": "test"}, "exports": {".": {"types": "./development/index.d.ts", "browser": {"development": "./development/index.js", "default": "./index.js"}, "node": {"development": "./node/development/index.js", "default": "./node/index.js"}, "development": "./development/index.js", "default": "./index.js"}, "./use-controller.js": {"types": "./development/use-controller.d.ts", "browser": {"development": "./development/use-controller.js", "default": "./use-controller.js"}, "node": {"development": "./node/development/use-controller.js", "default": "./node/use-controller.js"}, "development": "./development/use-controller.js", "default": "./use-controller.js"}}, "files": ["/development/", "!/development/test/", "/node/", "/index.{d.ts,d.ts.map,js,js.map}", "/create-component.{d.ts,d.ts.map,js,js.map}", "/use-controller.{d.ts,d.ts.map,js,js.map}"], "scripts": {"build": "wireit", "build:ts": "wireit", "build:ts:types": "wireit", "build:rollup": "wireit", "test": "wireit", "test:dev": "wireit", "test:prod": "wireit", "test:node": "wireit", "checksize": "wireit"}, "wireit": {"build": {"dependencies": ["build:rollup", "build:ts", "build:ts:types"]}, "build:ts": {"command": "tsc --build --pretty", "dependencies": ["../reactive-element:build:ts:types"], "clean": "if-file-deleted", "files": ["src/**/*.ts", "src/**/*.tsx", "tsconfig.json"], "output": ["development", "tsconfig.tsbuildinfo"]}, "build:ts:types": {"command": "treemirror development . \"**/*.d.ts{,.map}\" \"!test\"", "dependencies": ["../internal-scripts:build", "build:ts"], "files": [], "output": ["*.d.ts{,.map}"]}, "build:rollup": {"command": "rollup -c", "dependencies": ["build:ts", "../..:rollup-config"], "files": ["rollup.config.js", "../../../rollup-common.js"], "output": ["create-component.js{,.map}", "index.js{,.map}", "use-controller.js{,.map}", "node/**/*.js{,.map}"]}, "checksize": {"command": "rollup -c --environment=CHECKSIZE", "dependencies": ["build:ts", "../..:rollup-config"], "files": ["rollup.config.js", "../../../rollup-common.js"], "output": []}, "test": {"dependencies": ["test:dev", "test:prod", "test:node"]}, "test:dev": {"#comment": "Test files must also be specified in web-test-runner.config.js rollup config", "command": "node ../tests/run-web-tests.js \"development/**/*_test.js\" --config web-test-runner.config.js", "dependencies": ["build:ts", "build:rollup", "../reactive-element:build", "../tests:build"], "env": {"BROWSERS": {"external": true}, "MODE": "dev"}, "files": ["web-test-runner.config.js"], "output": []}, "test:prod": {"#comment": "Test files must also be specified in web-test-runner.config.js rollup config", "command": "node ../tests/run-web-tests.js \"development/**/*_test.js\" --config web-test-runner.config.js", "dependencies": ["build:ts", "build:rollup", "../reactive-element:build", "../tests:build"], "env": {"BROWSERS": {"external": true}, "MODE": "prod"}, "files": ["web-test-runner.config.js"], "output": []}, "test:node": {"command": "node development/test/node-render.js", "dependencies": ["build:ts", "build:rollup", "../reactive-element:build"], "files": [], "output": []}}, "author": "Google LLC", "devDependencies": {"@lit/reactive-element": "^2.0.4", "@lit-internal/scripts": "^1.0.1", "@types/react-dom": "^18.2.6", "@types/trusted-types": "^2.0.2", "@web/dev-server-rollup": "^0.5.2", "react": "^18.2.0", "react-dom": "^18.2.0"}, "peerDependencies": {"@types/react": "17 || 18 || 19"}, "publishConfig": {"access": "public"}}