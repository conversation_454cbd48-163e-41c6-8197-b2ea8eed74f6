figma.showUI(__uiFiles__.ui, { width: 400, height: 600 });

// --- Load KPIs Dynamically ---
async function loadKPIs(): Promise<any[]> {
  try {
    const response = await fetch('public/data/master_kpis.json');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error loading KPIs:', error);
    return [];
  }
}

// --- Handle Messages from the UI ---
figma.ui.onmessage = async (msg) => {
  switch (msg.type) {
    case 'show-browse':
      figma.showUI(__uiFiles__.browse, { width: 400, height: 600 });
      break;

    case 'show-wizard':
      figma.showUI(__uiFiles__.wizard, { width: 400, height: 600 });
      break;

    case 'show-start':
      figma.showUI(__uiFiles__.ui, { width: 400, height: 600 });
      break;
      
    case 'show-loading':
      figma.showUI(__uiFiles__.loading, { width: 400, height: 600 });
      break;

    case 'loading-complete':
      // Simulate KPI recommendation process
      setTimeout(() => {
        figma.showUI(__uiFiles__.browse, { width: 400, height: 600 });
      }, 1000);
      break;

    default:
      console.log('Unknown message type:', msg.type);
  }
};
