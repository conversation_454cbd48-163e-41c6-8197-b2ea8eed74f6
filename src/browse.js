// Import CSS files
import '../ui/styles/global.css';
import '../ui/styles/browse-styles.css';

// Your browse page JavaScript code here
document.addEventListener('DOMContentLoaded', () => {
  const backBtn = document.getElementById('backBtn');
  
  if (backBtn) {
    backBtn.addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-start' } }, '*');
    });
  }
  
  // You could load KPIs dynamically here
  // For example:
  // loadKPIs().then(kpis => {
  //   renderKPIs(kpis);
  // });
});

function renderKPIs(kpis) {
  const kpiList = document.getElementById('kpiList');
  if (!kpiList) return;
  
  kpiList.innerHTML = '';
  
  kpis.forEach(kpi => {
    const kpiCard = document.createElement('div');
    kpiCard.className = 'kpi-card';
    
    const title = document.createElement('h3');
    title.textContent = kpi.name;
    
    const description = document.createElement('p');
    description.textContent = kpi.description;
    
    kpiCard.appendChild(title);
    kpiCard.appendChild(description);
    kpiList.appendChild(kpiCard);
  });
}