// Import CSS files
import '../ui/styles/global.css';
import '../ui/styles/wizard-styles.css';

// Your wizard page JavaScript code here
document.addEventListener('DOMContentLoaded', () => {
  const backBtn = document.getElementById('backBtn');
  const nextBtn = document.getElementById('nextBtn');
  const optionButtons = document.querySelectorAll('.option-button');
  
  if (backBtn) {
    backBtn.addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-start' } }, '*');
    });
  }
  
  if (nextBtn) {
    nextBtn.addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-loading' } }, '*');
    });
  }
  
  // Add click handlers for option buttons
  optionButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remove selected class from all buttons
      optionButtons.forEach(btn => btn.classList.remove('selected'));
      
      // Add selected class to clicked button
      button.classList.add('selected');
    });
  });
});