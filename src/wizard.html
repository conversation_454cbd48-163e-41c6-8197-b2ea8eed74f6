<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="initial-scale=1, width=device-width">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&display=swap" rel="stylesheet">

  <!-- Shoelace CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/themes/light.css">

  <!-- Custom Styles -->
  <link rel="stylesheet" href="./global.css" />

</head>
<body>
  <div class="container">
    <div class="title">
      Which <span class="text-primary">category</span> best fits your product?
    </div>
    <div class="pagination">
      <div class="pagination-dot"></div>
      <div class="pagination-dot inactive"></div>
      <div class="pagination-dot inactive"></div>
      <div class="pagination-dot inactive"></div>
      <div class="pagination-dot inactive"></div>
      <div class="pagination-dot inactive"></div>
    </div>
    <div class="grid-2col">
      <sl-card class="card-pill" data-value="saas">
        <div class="card-text">SaaS & Productivity</div>
      </sl-card>
      <sl-card class="card-pill" data-value="marketplace">
        <div class="card-text">Marketplace & Ecomm</div>
      </sl-card>
      <sl-card class="card-pill" data-value="fintech">
        <div class="card-text">Fintech</div>
      </sl-card>
      <sl-card class="card-pill" data-value="edtech">
        <div class="card-text">EdTech</div>
      </sl-card>
      <sl-card class="card-pill" data-value="health">
        <div class="card-text">Health & Wellness</div>
      </sl-card>
      <sl-card class="card-pill" data-value="travel">
        <div class="card-text">Travel & Hospitality</div>
      </sl-card>
      <sl-card class="card-pill" data-value="logistics">
        <div class="card-text">Logistics & Delivery</div>
      </sl-card>
      <sl-card class="card-pill" data-value="gaming">
        <div class="card-text">Gaming</div>
      </sl-card>
      <sl-card class="card-pill" data-value="social">
        <div class="card-text">Social & Media</div>
      </sl-card>
      <sl-card class="card-pill" data-value="other">
        <div class="card-text">Other</div>
      </sl-card>
    </div>
    <div class="navigation">
      <sl-button variant="default" class="btn-secondary" id="backBtn">
        <sl-icon slot="prefix" name="arrow-left"></sl-icon>
        Back
      </sl-button>
      <sl-button variant="primary" class="btn-primary" id="nextBtn" disabled style="flex:1; margin-left: 12px;">
        Next
        <sl-icon slot="suffix" name="arrow-right"></sl-icon>
      </sl-button>
    </div>
  </div>

  <script type="module" src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/shoelace-autoloader.js"></script>
  <script type="module">
    let selectedCategory = null;
    let step = 1;
    document.querySelectorAll('.card-pill').forEach(option => {
      option.addEventListener('click', () => {
        document.querySelectorAll('.card-pill').forEach(opt => {
          opt.classList.remove('selected');
        });
        option.classList.add('selected');
        selectedCategory = option.dataset.value;
        document.getElementById('nextBtn').disabled = false;
      });
    });
    document.getElementById('backBtn').addEventListener('click', () => {
      if (step === 1) {
        parent.postMessage({ pluginMessage: { type: 'show-start' } }, '*');
      } else {
        // TODO: handle going back to previous wizard step
      }
    });
    document.getElementById('nextBtn').addEventListener('click', () => {
      if (selectedCategory && step === 1) {
        parent.postMessage({ pluginMessage: { type: 'wizard-step', step: 2, data: { category: selectedCategory } } }, '*');
        parent.postMessage({ pluginMessage: { type: 'show-loading' } }, '*');
      }
    });
    document.addEventListener('focusin', (e) => {
      e.preventDefault();
      e.stopPropagation();
    });
  </script>
</body>
</html>