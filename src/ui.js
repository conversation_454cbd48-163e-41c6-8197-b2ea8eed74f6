// Import CSS files
import '../ui/styles/global.css';
import '../ui/styles/ui-styles.css';

// Your UI JavaScript code here
document.addEventListener('DOMContentLoaded', () => {
  // Initialize UI components
  const browseBtn = document.getElementById('browseBtn');
  const wizardBtn = document.getElementById('wizardBtn');
  
  if (browseBtn) {
    browseBtn.addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-browse' } }, '*');
    });
  }
  
  if (wizardBtn) {
    wizardBtn.addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-wizard' } }, '*');
    });
  }
});
