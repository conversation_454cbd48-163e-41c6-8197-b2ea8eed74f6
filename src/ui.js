// Import CSS files
import './global.css';
import './ui-styles.css';

// Your UI JavaScript code here
document.addEventListener('DOMContentLoaded', () => {
  // Initialize UI components
  const browseBtn = document.getElementById('browseBtn');
  const wizardBtn = document.getElementById('wizardBtn');
  
  if (browseBtn) {
    browseBtn.addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'browse-kpis' } }, '*');
    });
  }
  
  if (wizardBtn) {
    wizardBtn.addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'start-wizard' } }, '*');
    });
  }
});