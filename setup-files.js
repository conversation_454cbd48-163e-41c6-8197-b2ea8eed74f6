const fs = require('fs');
const path = require('path');

// Create directories if they don't exist
const directories = [
  'src',
  'src/utils',
  'ui',
  'ui/styles',
  'ui/assets',
  'public',
  'public/data',
  'public/images',
  'public/logo'
];

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
});

// Check if UI files exist in src directory and move them to ui directory
const filesToMove = [
  { from: 'src/ui.html', to: 'ui/ui.html' },
  { from: 'src/browse.html', to: 'ui/browse.html' },
  { from: 'src/wizard.html', to: 'ui/wizard.html' },
  { from: 'src/loading.html', to: 'ui/loading.html' },
  { from: 'src/global.css', to: 'ui/styles/global.css' },
  { from: 'src/ui-styles.css', to: 'ui/styles/ui-styles.css' }
];

filesToMove.forEach(file => {
  if (fs.existsSync(file.from) && !fs.existsSync(file.to)) {
    // Create the directory if it doesn't exist
    const dir = path.dirname(file.to);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Copy the file
    fs.copyFileSync(file.from, file.to);
    console.log(`Copied ${file.from} to ${file.to}`);
  }
});

// Create basic UI files if they don't exist
const uiFiles = [
  {
    path: 'ui/ui.html',
    content: `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Product KPIs</title>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 20px;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    button {
      padding: 10px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Product KPIs</h1>
    <button id="browseBtn">Browse KPIs</button>
    <button id="wizardBtn">KPI Wizard</button>
  </div>
  <script>
    document.getElementById('browseBtn').addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-browse' } }, '*');
    });
    document.getElementById('wizardBtn').addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-wizard' } }, '*');
    });
  </script>
</body>
</html>`
  },
  {
    path: 'ui/browse.html',
    content: `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Browse KPIs</title>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 20px;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    button {
      padding: 10px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Browse KPIs</h1>
    <div id="kpiList"></div>
    <button id="backBtn">Back</button>
  </div>
  <script>
    document.getElementById('backBtn').addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-main' } }, '*');
    });
  </script>
</body>
</html>`
  },
  {
    path: 'ui/wizard.html',
    content: `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>KPI Wizard</title>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 20px;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    button {
      padding: 10px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>KPI Wizard</h1>
    <div id="wizardContent"></div>
    <button id="backBtn">Back</button>
  </div>
  <script>
    document.getElementById('backBtn').addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-main' } }, '*');
    });
  </script>
</body>
</html>`
  },
  {
    path: 'ui/loading.html',
    content: `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Loading</title>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }
    .loading {
      text-align: center;
    }
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid #3498db;
      width: 40px;
      height: 40px;
      animation: spin 2s linear infinite;
      margin: 0 auto 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="loading">
    <div class="spinner"></div>
    <h2>Loading...</h2>
    <p>Finding the perfect KPIs for your product</p>
  </div>
  <script>
    // Simulate loading process
    setTimeout(() => {
      parent.postMessage({ pluginMessage: { type: 'loading-complete' } }, '*');
    }, 3000);
  </script>
</body>
</html>`
  }
];

uiFiles.forEach(file => {
  if (!fs.existsSync(file.path)) {
    // Create the directory if it doesn't exist
    const dir = path.dirname(file.path);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Create the file
    fs.writeFileSync(file.path, file.content);
    console.log(`Created file: ${file.path}`);
  }
});

// Check if code.ts exists in root and move it to src if needed
if (fs.existsSync('code.ts') && !fs.existsSync('src/code.ts')) {
  fs.copyFileSync('code.ts', 'src/code.ts');
  console.log('Copied code.ts to src/code.ts');
}

// Create a basic code.ts if it doesn't exist
if (!fs.existsSync('src/code.ts')) {
  const codeTs = `// This is the main entry point for the Figma plugin
figma.showUI(__uiFiles__.start, { width: 400, height: 600 });

// Handle messages from the UI
figma.ui.onmessage = async (msg) => {
  if (msg.type === 'show-browse') {
    figma.showUI(__uiFiles__.browse, { width: 400, height: 600 });
  } else if (msg.type === 'show-wizard') {
    figma.showUI(__uiFiles__.wizard, { width: 400, height: 600 });
  } else if (msg.type === 'show-main') {
    figma.showUI(__uiFiles__.start, { width: 400, height: 600 });
  } else if (msg.type === 'loading-complete') {
    // Handle loading complete
  }
};
`;
  fs.writeFileSync('src/code.ts', codeTs);
  console.log('Created src/code.ts');
}

// Update manifest.json if it exists
if (fs.existsSync('manifest.json')) {
  const manifest = JSON.parse(fs.readFileSync('manifest.json', 'utf8'));

  // Update UI paths
  if (manifest.ui) {
    manifest.ui.start = 'dist/src/ui.html';
    manifest.ui.browse = 'dist/src/browse.html';
    manifest.ui.wizard = 'dist/src/wizard.html';
    manifest.ui.loading = 'dist/src/loading.html';
  }

  fs.writeFileSync('manifest.json', JSON.stringify(manifest, null, 2));
  console.log('Updated manifest.json');
}

console.log('Setup complete!');