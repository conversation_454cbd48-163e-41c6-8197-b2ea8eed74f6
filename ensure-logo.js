const fs = require('fs');
const path = require('path');

// Define paths
const logoSourcePath = path.resolve(__dirname, 'public/logo/Logo4.svg');
const distLogoDir = path.resolve(__dirname, 'dist/logo');

// Ensure the logo directory exists in dist
if (!fs.existsSync(distLogoDir)) {
  fs.mkdirSync(distLogoDir, { recursive: true });
  console.log('Created logo directory in dist folder');
}

// Check if the logo exists
if (fs.existsSync(logoSourcePath)) {
  console.log('Logo found at:', logoSourcePath);
} else {
  console.error('Logo not found at:', logoSourcePath);
  process.exit(1);
}

console.log('Logo verification complete');