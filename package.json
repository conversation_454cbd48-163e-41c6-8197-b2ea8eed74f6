{"name": "product-kpi-plugin", "version": "1.0.0", "description": "A Figma plugin for managing and visualizing product KPIs", "main": "dist/code.js", "scripts": {"setup": "node setup-files.js", "prebuild": "npm run setup && rm -rf dist && mkdir -p dist && node ensure-logo.js", "build": "tsc -p tsconfig.json && vite build && npm run post-build", "post-build": "cp -r public/* dist/ 2>/dev/null || true", "dev": "npm run setup && tsc -p tsconfig.json && vite build --watch", "build-ts": "tsc -p tsconfig.json", "watch": "tsc -p tsconfig.json --watch", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -f code.js && rm -rf dist"}}