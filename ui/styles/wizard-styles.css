/* Styles specific to the wizard page */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 16px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  position: relative;
}

.back-button {
  background: none;
  border: none;
  font-size: 14px;
  color: #666;
  padding: 4px 8px;
  position: absolute;
  left: 0;
}

.title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 auto;
}

.content {
  flex: 1;
  overflow-y: auto;
}

.wizard-step {
  margin-bottom: 24px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-button {
  background-color: #f0f0f0;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
  text-align: left;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.option-button:hover {
  background-color: #e8e8e8;
}

.option-button.selected {
  background-color: #e1f0ff;
  border-color: #0d99ff;
}

.footer {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}