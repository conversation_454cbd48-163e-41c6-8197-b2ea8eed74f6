/* Global styles for all pages */
body {
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background-color: #ffffff;
  color: #333333;
}

* {
  box-sizing: border-box;
}

button {
  cursor: pointer;
  font-family: 'Inter', sans-serif;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

button:hover {
  opacity: 0.9;
}

button:active {
  opacity: 0.8;
}

.primary-button {
  background-color: #0d99ff;
  color: white;
}

.secondary-button {
  background-color: #f0f0f0;
  color: #333333;
}

/* Common Container Styles */
.container {
  width: 100%;
  height: 100%;
  padding: 16px 24px 12px 24px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

/* Typography */
.title {
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 16px;
  line-height: 1.3;
  color: #1a1a1a;
}

.title-large {
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 16px;
  line-height: 1.2;
  color: #1a1a1a;
}

.subtitle {
  font-size: 14px;
  color: #666666;
  text-align: center;
  line-height: 1.5;
}

/* Brand Colors */
.text-primary {
  color: #0C8CE9;
}

/* Pagination Dots */
.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.pagination-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #0C8CE9;
}

.pagination-dot.inactive {
  background-color: #e0e0e0;
}

/* Grid Layouts */
.grid-2col {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 12px;
  flex: 1;
  align-content: start;
}

/* Card Styles */
.card-pill {
  cursor: pointer;
  transition: all 0.2s ease;
}

.card-pill::part(base) {
  border-radius: 24px;
  padding: 8px 6px;
  text-align: center;
  min-height: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
  border: 1.5px solid #e0e0e0;
  transition: border-color 0.2s, background 0.2s;
}

.card-pill:hover::part(base) {
  border-color: #0C8CE9;
}

.card-pill.selected::part(base) {
  border-color: #0C8CE9;
  background: #eaf4fe;
}

.card-pill .card-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
}

/* Full-width cards for lists */
.card-list {
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.card-list::part(base) {
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.card-list:hover::part(base) {
  border-color: #0C8CE9;
  box-shadow: 0 4px 12px rgba(12, 140, 233, 0.1);
}

.card-list [slot="header"] {
  margin-bottom: 8px;
}

.card-list [slot="header"] strong {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-list p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.card-list [slot="footer"] {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

/* Navigation */
.navigation {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 0;
  margin-top: 12px;
  background: #fff;
  box-shadow: 0 0 8px 0 rgba(0,0,0,0.03);
  padding: 0;
  border-radius: 12px;
  flex-shrink: 0;
}

.navigation-simple {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

/* Button Styles */
.btn-primary {
  min-width: 120px;
  height: 36px;
  font-size: 14px;
  font-weight: 600;
}

.btn-secondary {
  min-width: 80px;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
}

.btn-large {
  height: 48px;
  font-size: 14px;
  font-weight: 500;
}

/* Search and Filters */
.search-container {
  position: relative;
  margin-bottom: 12px;
}

.search-container sl-input {
  width: 100%;
}

.filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 12px;
}

.filters sl-button-group {
  display: flex;
  flex-wrap: wrap;
}

/* List Containers */
.list-container {
  flex: 1;
  overflow-y: auto;
  margin-top: 12px;
  padding-right: 4px;
}

/* Loading Styles */
.loading-container {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1a1a1a;
}

.loading-message {
  font-size: 14px;
  color: #666;
}

.sparkles {
  font-size: 24px;
  margin-bottom: 16px;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

/* Shoelace Component Overrides */
sl-spinner {
  font-size: 48px;
  margin-bottom: 24px;
  --indicator-color: #0C8CE9;
}

/* Utility Classes */
.flex-1 {
  flex: 1;
}

.text-center {
  text-align: center;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-16 {
  margin-top: 16px;
}

/* Focus Prevention for Figma */
*:focus {
  outline: none !important;
} 
