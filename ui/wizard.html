<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <!-- Load fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Import CSS via JavaScript -->
  <script type="module">
    import './styles/global.css';
    import './styles/wizard-styles.css';
  </script>
</head>
<body>
  <div class="container">
    <div class="header">
      <button id="backBtn" class="back-button">← Back</button>
      <h1 class="title">KPI Wizard</h1>
    </div>
    
    <div class="content">
      <div class="wizard-step">
        <h2 class="step-title">What type of product are you working on?</h2>
        <div class="options">
          <button class="option-button">Mobile App</button>
          <button class="option-button">Web Application</button>
          <button class="option-button">E-commerce</button>
          <button class="option-button">SaaS</button>
          <button class="option-button">Other</button>
        </div>
      </div>
    </div>
    
    <div class="footer">
      <button id="nextBtn" class="primary-button">Next</button>
    </div>
  </div>
  
  <script>
    document.getElementById('backBtn').addEventListener('click', () => {
      parent.postMessage({ pluginMessage: { type: 'show-start' } }, '*');
    });
    
    document.getElementById('nextBtn').addEventListener('click', () => {
      // Show loading screen
      parent.postMessage({ pluginMessage: { type: 'show-loading' } }, '*');
    });
  </script>
</body>
</html> 
