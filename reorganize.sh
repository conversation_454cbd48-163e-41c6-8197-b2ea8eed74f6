#!/bin/bash

# Create new directory structure
mkdir -p src/utils
mkdir -p ui/components
mkdir -p ui/styles
mkdir -p ui/assets
mkdir -p public/data
mkdir -p public/images

# Move TypeScript files to src
mv code.ts src/
mv utils/kpi-recommender.js src/utils/
mv utils/svg-optimizer.js src/utils/

# Move UI files to ui directory
mv src/ui.html ui/
mv src/browse.html ui/
mv src/wizard.html ui/
mv src/loading.html ui/
mv src/global.css ui/styles/
mv src/ui-styles.css ui/styles/

# Move data files to public
mv master_kpis.json public/data/
mv public/logo public/images/logo

# Update references in code
sed -i '' 's|src/ui.html|ui/ui.html|g' vite.config.js
sed -i '' 's|src/browse.html|ui/browse.html|g' vite.config.js
sed -i '' 's|src/wizard.html|ui/wizard.html|g' vite.config.js
sed -i '' 's|src/loading.html|ui/loading.html|g' vite.config.js

# Update manifest.json
sed -i '' 's|"start": "dist/ui.html"|"start": "dist/ui/ui.html"|g' manifest.json
sed -i '' 's|"browse": "dist/browse.html"|"browse": "dist/ui/browse.html"|g' manifest.json
sed -i '' 's|"wizard": "dist/wizard.html"|"wizard": "dist/ui/wizard.html"|g' manifest.json
sed -i '' 's|"loading": "dist/loading.html"|"loading": "dist/ui/loading.html"|g' manifest.json

# Update tsconfig.json
sed -i '' 's|"include": \["code.ts", "src/\*\*/\*.ts", "utils/\*\*/\*.ts"\]|"include": \["src/\*\*/\*.ts"\]|g' tsconfig.json