import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    outDir: 'dist',
    emptyOutDir: false, // Don't empty the outDir to preserve TypeScript output
    rollupOptions: {
      input: {
        ui: resolve(__dirname, 'ui/ui.html'),
        browse: resolve(__dirname, 'ui/browse.html'),
        wizard: resolve(__dirname, 'ui/wizard.html'),
        loading: resolve(__dirname, 'ui/loading.html')
      }
    }
  },
  publicDir: 'public'
});
