import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    outDir: 'dist',
    emptyOutDir: false, // Don't empty the outDir to preserve TypeScript output
    rollupOptions: {
      input: {
        ui: resolve(__dirname, 'src/ui.html'),
        browse: resolve(__dirname, 'src/browse.html'),
        wizard: resolve(__dirname, 'src/wizard.html'),
        loading: resolve(__dirname, 'src/loading.html')
      }
    }
  },
  publicDir: 'public'
});
